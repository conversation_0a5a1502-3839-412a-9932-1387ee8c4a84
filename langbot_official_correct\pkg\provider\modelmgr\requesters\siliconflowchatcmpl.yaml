apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: siliconflow-chat-completions
  label:
    en_US: SiliconFlow
    zh_Hans: 硅基流动
  icon: siliconflow.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.siliconflow.cn/v1"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./siliconflowchatcmpl.py
    attr: SiliconFlowChatCompletions
