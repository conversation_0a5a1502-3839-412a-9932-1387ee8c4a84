"""
统一会话管理器
整合所有工作流类型的会话管理功能
"""

import time
import uuid
from typing import Dict, Optional, List, Tuple, Any
from .models import WorkflowSession, WorkflowType, SessionState, SessionImage, FluxImageType
from .states import (
    is_execution_command, 
    is_cancel_command, 
    get_next_state_for_workflow,
    can_transition_to
)


class SessionManager:
    """统一会话管理器 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, cleanup_interval: int = 300):
        """
        初始化会话管理器（单例模式）
        
        Args:
            cleanup_interval: 清理过期会话的间隔（秒）
        """
        if self._initialized:
            return
            
        self.sessions: Dict[str, WorkflowSession] = {}  # {session_id: session}
        self.user_sessions: Dict[str, str] = {}  # {user_key: session_id}
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = time.time()
        self.logger = None
        self._initialized = True
    
    def set_logger(self, logger):
        """设置日志器"""
        self.logger = logger
    
    def _log(self, message: str, level: str = "info"):
        """记录日志"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[SessionManager] {message}")
            elif level == "warning":
                self.logger.warning(f"[SessionManager] {message}")
            else:
                self.logger.info(f"[SessionManager] {message}")
    
    def _get_user_key(self, user_id: str, chat_id: str = "") -> str:
        """获取用户键"""
        return f"{user_id}::{chat_id}" if chat_id else user_id
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4())
    
    def cleanup_expired_sessions(self) -> int:
        """
        清理过期会话
        
        Returns:
            清理的会话数量
        """
        current_time = time.time()
        if current_time - self.last_cleanup < self.cleanup_interval:
            return 0
        
        expired_sessions = []
        for session_id, session in self.sessions.items():
            if session.is_expired():
                expired_sessions.append(session_id)
        
        cleaned_count = 0
        for session_id in expired_sessions:
            self._remove_session(session_id)
            cleaned_count += 1
        
        self.last_cleanup = current_time
        
        if cleaned_count > 0:
            self._log(f"清理了 {cleaned_count} 个过期会话")
        
        return cleaned_count
    
    def _remove_session(self, session_id: str) -> bool:
        """
        移除会话
        
        Returns:
            是否成功移除
        """
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        user_key = self._get_user_key(session.user_id, session.chat_id)
        
        # 移除用户会话映射
        if user_key in self.user_sessions and self.user_sessions[user_key] == session_id:
            del self.user_sessions[user_key]
        
        # 移除会话
        del self.sessions[session_id]
        
        self._log(f"移除会话 {session_id} (用户: {session.user_id})")
        return True
    
    def create_session(
        self, 
        user_id: str, 
        workflow_type: WorkflowType, 
        chat_id: str = "",
        prompt: str = "",
        timeout_minutes: int = 10,
        max_images: Optional[int] = None,
        min_images: Optional[int] = None
    ) -> WorkflowSession:
        """
        创建新会话
        
        Args:
            user_id: 用户ID
            workflow_type: 工作流类型
            chat_id: 聊天ID
            prompt: 初始提示词
            timeout_minutes: 超时时间（分钟）
            max_images: 最大图片数（None使用默认值）
            min_images: 最小图片数（None使用默认值）
            
        Returns:
            创建的会话对象
        """
        # 清理过期会话
        self.cleanup_expired_sessions()
        
        # 检查用户是否已有活跃会话
        user_key = self._get_user_key(user_id, chat_id)
        if user_key in self.user_sessions:
            old_session_id = self.user_sessions[user_key]
            self._remove_session(old_session_id)
            self._log(f"用户 {user_id} 已有活跃会话，替换为新会话")
        
        # 生成会话ID
        session_id = self._generate_session_id()
        
        # 创建新会话
        session = WorkflowSession(
            session_id=session_id,
            user_id=user_id,
            chat_id=chat_id,
            workflow_type=workflow_type,
            prompt=prompt,
            timeout_minutes=timeout_minutes
        )
        
        # 设置图片限制
        if max_images is not None:
            session.max_images = max_images
        if min_images is not None:
            session.min_images = min_images
        
        # 设置初始状态
        session.state = get_next_state_for_workflow(
            workflow_type.value,
            session.has_prompt(),
            session.get_image_count() > 0
        )
        
        # 保存会话
        self.sessions[session_id] = session
        self.user_sessions[user_key] = session_id
        
        self._log(f"创建新会话 {session_id} (用户: {user_id}, 类型: {workflow_type.value})")
        return session
    
    def get_session(self, user_id: str, chat_id: str = "") -> Optional[WorkflowSession]:
        """
        获取用户的活跃会话
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            会话对象或None
        """
        self.cleanup_expired_sessions()
        
        user_key = self._get_user_key(user_id, chat_id)
        if user_key not in self.user_sessions:
            return None
        
        session_id = self.user_sessions[user_key]
        session = self.sessions.get(session_id)
        
        if not session:
            # 会话不存在，清理映射
            del self.user_sessions[user_key]
            return None
        
        if session.is_expired():
            # 会话已过期，清理
            self._remove_session(session_id)
            return None
        
        return session
    
    def get_session_by_id(self, session_id: str) -> Optional[WorkflowSession]:
        """
        根据会话ID获取会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话对象或None
        """
        session = self.sessions.get(session_id)
        if session and not session.is_expired():
            return session
        elif session and session.is_expired():
            self._remove_session(session_id)
        return None
    
    def delete_session(self, user_id: str, chat_id: str = "") -> bool:
        """
        删除用户会话

        Args:
            user_id: 用户ID
            chat_id: 聊天ID

        Returns:
            是否成功删除
        """
        session = self.get_session(user_id, chat_id)
        if session:
            return self._remove_session(session.session_id)
        return False

    def cleanup_session_data(self, user_id: str, chat_id: str = "") -> bool:
        """
        彻底清理会话数据，确保所有信息都被清除

        Args:
            user_id: 用户ID
            chat_id: 聊天ID

        Returns:
            是否成功清理
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False

        try:
            # 清理所有数据
            session.images.clear()
            session.messages.clear()
            session.prompt = ""
            session.quoted_text = ""
            session.quoted_images.clear()
            session.parameters.clear()
            session.routing_result = None
            session.forced_workflow_file = None

            # 重置状态
            session.state = SessionState.CANCELLED

            self._log(f"会话 {session.session_id} 数据已彻底清理")
            return True

        except Exception as e:
            self._log(f"清理会话 {session.session_id} 数据失败: {e}")
            return False
    
    def add_image_to_session(
        self, 
        user_id: str, 
        image_data: bytes, 
        chat_id: str = "",
        purpose: str = "reference",
        source: str = "upload"
    ) -> Tuple[bool, str, Optional[WorkflowSession]]:
        """
        向会话添加图片
        
        Args:
            user_id: 用户ID
            image_data: 图片数据
            chat_id: 聊天ID
            purpose: 图片用途
            source: 图片来源
            
        Returns:
            (是否成功, 消息, 会话对象)
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False, "没有活跃的会话，请先发送工作流指令", None
        
        # 检查状态是否允许添加图片
        if session.state not in [
            SessionState.COLLECTING, 
            SessionState.WAITING_FOR_IMAGES, 
            SessionState.READY_FOR_GENERATION,
            SessionState.READY  # 添加READY状态，允许在准备就绪时也能添加图片
        ]:
            return False, f"当前状态 '{session.state.value}' 不允许添加图片", session
        
        # 检查图片数量限制
        if not session.can_add_image():
            return False, f"⚠️ 最多只能提供{session.max_images}张图片，已忽略多余图片", session
        
        # 添加图片
        if not session.add_image(image_data, purpose, source):
            return False, "图片已存在，未重复添加", session
        
        # 更新状态
        session.state = get_next_state_for_workflow(
            session.workflow_type.value,
            session.has_prompt(),
            session.get_image_count() > 0
        )
        
        # 生成成功消息 - 🔥 改进：添加图片数量提示
        current_count = session.get_image_count()
        max_count = session.max_images
        success_msg = f"📷 收到第{current_count}张图片 ({current_count}/{max_count})"
        
        if session.state == SessionState.READY_FOR_GENERATION:
            success_msg += f"，可发送指令： 开始 |  go  | 取消"
        elif session.state == SessionState.READY:
            success_msg += f"，可发送指令： 开始 |  go  | 取消"
        elif session.can_add_image():
            remaining = max_count - current_count
            success_msg += f"，还可以上传{remaining}张，可发送指令： 开始 |  go  | 取消"
        
        success_msg += " ✅"
        
        self._log(f"会话 {session.session_id} 添加图片，当前数量: {session.get_image_count()}")
        return True, success_msg, session
    
    def update_session_prompt(
        self, 
        user_id: str, 
        prompt: str, 
        chat_id: str = ""
    ) -> Tuple[bool, str, Optional[WorkflowSession]]:
        """
        更新会话提示词
        
        Args:
            user_id: 用户ID
            prompt: 新提示词
            chat_id: 聊天ID
            
        Returns:
            (是否成功, 消息, 会话对象)
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False, "没有活跃的会话", None
        
        session.set_prompt(prompt)
        
        # 更新状态
        session.state = get_next_state_for_workflow(
            session.workflow_type.value,
            session.has_prompt(),
            session.get_image_count() > 0
        )
        
        success_msg = "提示词已更新 ✅"
        if session.state == SessionState.READY_FOR_GENERATION:
            success_msg += "，可发送指令： 开始 |  go  | 取消"
        elif session.state == SessionState.WAITING_FOR_IMAGES:
            success_msg += f"，还需要提供 {session.min_images} 张图片才能开始生成"
        
        self._log(f"会话 {session.session_id} 更新提示词: {prompt[:50]}...")
        return True, success_msg, session
    
    def trigger_execution(
        self, 
        user_id: str, 
        chat_id: str = ""
    ) -> Tuple[bool, str, Optional[WorkflowSession]]:
        """
        触发会话执行
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            (是否成功, 消息, 会话对象)
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False, "没有活跃的会话", None
        
        if not session.is_ready_for_execution():
            missing_items = []
            if not session.has_prompt() and session.workflow_type != WorkflowType.AIGEN:
                missing_items.append("提示词")
            if not session.has_enough_images() and session.workflow_type != WorkflowType.AIGEN:
                missing_items.append(f"图片（至少{session.min_images}张）")
            
            if missing_items:
                return False, f"会话未准备就绪，还需要：{', '.join(missing_items)}", session
            else:
                return False, f"会话状态 '{session.state.value}' 不允许执行", session
        
        # 更新状态为执行中
        if session.workflow_type == WorkflowType.AIGEN:
            session.state = SessionState.PROCESSING
        else:
            session.state = SessionState.GENERATING
        
        session.update_activity()
        
        self._log(f"会话 {session.session_id} 开始执行")
        return True, "", session
    
    def cancel_session(
        self, 
        user_id: str, 
        chat_id: str = ""
    ) -> Tuple[bool, str]:
        """
        取消会话
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            (是否成功, 消息)
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False, "没有活跃的会话"
        
        session.state = SessionState.CANCELLED
        self._remove_session(session.session_id)
        
        self._log(f"会话 {session.session_id} 已取消")
        return True, "会话已取消 ✅"
    
    def complete_session(
        self, 
        user_id: str, 
        chat_id: str = ""
    ) -> bool:
        """
        完成会话
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            是否成功完成
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False
        
        session.state = SessionState.COMPLETED
        self._remove_session(session.session_id)
        
        self._log(f"会话 {session.session_id} 已完成")
        return True
    
    def get_session_info(
        self, 
        user_id: str, 
        chat_id: str = ""
    ) -> Optional[Dict[str, Any]]:
        """
        获取会话详细信息
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            会话信息字典或None
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return None
        
        return session.get_session_summary()
    
    def process_user_input(
        self, 
        user_id: str, 
        user_text: str, 
        user_images: List[bytes], 
        chat_id: str = ""
    ) -> Tuple[str, Dict[str, Any]]:
        """
        处理用户输入的统一接口
        
        Args:
            user_id: 用户ID
            user_text: 用户文本
            user_images: 用户图片列表
            chat_id: 聊天ID
            
        Returns:
            (响应消息, 附加信息字典)
        """
        session = self.get_session(user_id, chat_id)
        
        # 如果有会话，处理会话内交互
        if session:
            # 检查取消指令
            if is_cancel_command(user_text):
                success, message = self.cancel_session(user_id, chat_id)
                return message, {'action': 'cancel', 'success': success}
            
            # 检查执行指令
            if is_execution_command(user_text):
                success, message, updated_session = self.trigger_execution(user_id, chat_id)
                return message, {
                    'action': 'execute', 
                    'success': success, 
                    'session': updated_session
                }
            
            # 🔥 新增：图片反推模式特殊处理
            if (session.prompt == '图片反推' and user_images):
                # 图片反推模式：有图片上传时自动触发图片转文本工作流
                self._log(f"检测到图片反推模式，用户上传了{len(user_images)}张图片")
                
                # 添加图片到会话（用于记录）
                for image_data in user_images:
                    session.add_image(image_data, "image_to_text", "upload")
                
                # 标记为准备执行图片转文本工作流
                session.state = SessionState.READY_FOR_GENERATION
                
                return "🖼️ 图片已接收，开始识别图片内容...", {
                    'action': 'execute_image_to_text',
                    'success': True,
                    'session': session,
                    'workflow_type': 'image_to_text'
                }
            
            # 处理Flux图片类型确认
            if session.state == SessionState.WAITING_FOR_IMAGE_TYPES:
                success, message, updated_session = self.process_flux_image_type_confirmation(
                    user_id, user_text, chat_id
                )
                return message, {
                    'action': 'image_type_confirmed',
                    'success': success,
                    'session': updated_session
                }
            
            # 处理图片添加
            if user_images:
                total_message = ""
                last_session = session
                for image_data in user_images:
                    success, message, updated_session = self.add_image_to_session(
                        user_id, image_data, chat_id
                    )
                    if success:
                        total_message = message  # 使用最后一个成功消息
                        last_session = updated_session
                    else:
                        total_message += f"\n{message}"
                
                # 🗑️ 旧的图片分类逻辑已移除
                # 图片分类现在由统一路由系统和工作流管理器自动处理
                # 基于智能路由选择的工作流子类型进行自动分类
                    
                    # 检查是否需要询问用户
                    ask_message = self.check_and_ask_image_types(last_session)
                    if ask_message:
                        total_message += f"\n\n{ask_message}"
                        return total_message, {
                            'action': 'image_added_with_question',
                            'session': last_session,
                            'needs_confirmation': True
                        }
                
                return total_message, {
                    'action': 'image_added', 
                    'session': last_session
                }
            
            # 处理提示词更新
            if user_text.strip():
                success, message, updated_session = self.update_session_prompt(
                    user_id, user_text, chat_id
                )
                return message, {
                    'action': 'prompt_updated', 
                    'success': success, 
                    'session': updated_session
                }
        
        # 没有会话或未能处理的情况
        return "请先开始一个工作流", {'action': 'no_session'}
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取会话管理器统计信息
        
        Returns:
            统计信息字典
        """
        self.cleanup_expired_sessions()
        
        stats = {
            'total_sessions': len(self.sessions),
            'active_users': len(self.user_sessions),
            'workflow_types': {},
            'session_states': {}
        }
        
        for session in self.sessions.values():
            # 统计工作流类型
            workflow_type = session.workflow_type.value
            stats['workflow_types'][workflow_type] = stats['workflow_types'].get(workflow_type, 0) + 1
            
            # 统计会话状态
            state = session.state.value
            stats['session_states'][state] = stats['session_states'].get(state, 0) + 1
        
        return stats
    
    def process_flux_image_type_confirmation(
        self, 
        user_id: str, 
        user_response: str, 
        chat_id: str = ""
    ) -> Tuple[bool, str, Optional[WorkflowSession]]:
        """
        处理Flux图片类型确认
        
        Args:
            user_id: 用户ID
            user_response: 用户回复
            chat_id: 聊天ID
            
        Returns:
            (是否成功, 消息, 会话对象)
        """
        session = self.get_session(user_id, chat_id)
        if not session:
            return False, "没有活跃的会话", None
        
        if session.workflow_type != WorkflowType.AIGEN:
            return False, "只有Flux工作流需要确认图片类型", session
        
        if session.state != SessionState.WAITING_FOR_IMAGE_TYPES:
            return False, f"当前状态 '{session.state.value}' 不需要确认图片类型", session
        
        image_count = session.get_image_count()
        if image_count == 0:
            return False, "没有图片需要确认类型", session
        
        # 解析用户回复
        success, message = self._parse_image_type_response(session, user_response)
        if not success:
            return False, message, session
        
        # 更新状态
        session.state = get_next_state_for_workflow(
            session.workflow_type.value,
            session.has_prompt(),
            session.get_image_count() > 0
        )
        
        success_msg = "✅ 图片类型已确认"
        if session.state == SessionState.READY:
            success_msg += "，可发送指令： 开始 |  go  | 取消"
        
        self._log(f"会话 {session.session_id} 确认图片类型完成")
        return True, success_msg, session
    
    def generate_workflow_execution_message(
        self,
        session: WorkflowSession,
        routing_result: Any = None,
        workflow_name: str = "",
        selected_loras: Optional[List[str]] = None
    ) -> str:
        """
        生成工作流执行开始消息

        Args:
            session: 会话对象
            routing_result: 路由结果（可选）
            workflow_name: 工作流名称（可选）
            selected_loras: 选择的LoRA模型列表（可选）

        Returns:
            格式化的执行消息
        """
        msg_parts = ["🎨 **Aigen工作流执行中**"]

        if routing_result:
            # 使用路由结果生成消息
            workflow_subtype = getattr(routing_result, 'workflow_subtype', None)
            confidence = getattr(routing_result, 'confidence', None)

            if workflow_subtype:
                msg_parts.append(f"🔧 **工作流**: {workflow_subtype.value}")
            if confidence:
                msg_parts.append(f"📊 **置信度**: {confidence.value}")

        elif workflow_name:
            # 使用工作流名称生成消息
            msg_parts.append(f"🔧 **工作流**: {workflow_name}")
            msg_parts.append(f"📊 **置信度**: user_selected")

        # 添加LoRA模型信息（在置信度之后）
        if selected_loras:
            lora_display = self._format_lora_names_for_execution(selected_loras)
            if lora_display:
                msg_parts.append(f"🎨 **LoRA**: {lora_display}")

        return "\n".join(msg_parts)

    def _format_lora_names_for_execution(self, lora_names: List[str]) -> str:
        """
        格式化LoRA名称用于执行消息显示

        Args:
            lora_names: LoRA名称列表

        Returns:
            格式化后的LoRA名称字符串
        """
        if not lora_names:
            return ""

        # 截断长名称并添加省略号
        formatted_names = []
        for name in lora_names[:3]:  # 最多显示3个
            if len(name) > 12:
                formatted_names.append(name[:12] + "...")
            else:
                formatted_names.append(name)

        if len(lora_names) > 3:
            return ", ".join(formatted_names) + f" 等{len(lora_names)}个"
        else:
            return ", ".join(formatted_names)
    
    def generate_workflow_completion_message(
        self, 
        execution_time: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        生成工作流完成消息
        
        Args:
            execution_time: 执行时间（秒）
            metadata: 元数据信息
            
        Returns:
            格式化的完成消息
        """
        if metadata is None:
            metadata = {}
        
        # 基础完成消息
        msg_parts = ["✅ 生成完成"]
        
        # 添加执行时间
        if execution_time > 0:
            msg_parts.append(f"⏱️ {int(execution_time)}s")
        
        # 添加种子信息（如果有）
        if metadata.get('seed'):
            msg_parts.append(f"🎲 {metadata.get('seed')}")
        
        # 添加Lora模型信息
        loras_used = metadata.get('loras_used', [])
        if loras_used:
            # 格式化LoRA名称（截断长名称）
            formatted_loras = []
            for lora_name in loras_used[:3]:  # 最多显示3个
                if len(lora_name) > 12:
                    formatted_loras.append(lora_name[:12] + "...")
                else:
                    formatted_loras.append(lora_name)

            # 构建显示文本
            if len(loras_used) <= 3:
                lora_display = ", ".join(formatted_loras)
            else:
                lora_display = ", ".join(formatted_loras) + f" 等{len(loras_used)}个"

            msg_parts.append(f"🎨 {lora_display}")
        else:
            # 没有LoRA模型时显示"no lora"
            msg_parts.append("🎨 no lora")
        
        return "，".join(msg_parts)
    
    def generate_optimized_prompt_message(self, optimized_prompt: str) -> str:
        """
        生成优化后的提示词消息
        
        Args:
            optimized_prompt: 优化后的英文提示词
            
        Returns:
            格式化的提示词消息
        """
        return optimized_prompt.strip()
    
    def generate_generating_status_message(self) -> str:
        """
        生成正在生成状态消息
        
        Returns:
            格式化的生成状态消息
        """
        return "⏳ 正在生成图片，请稍候..."
    
    def generate_workflow_error_message(self, error_message: str, error_type: str = "general") -> str:
        """
        生成工作流错误消息
        
        Args:
            error_message: 错误信息
            error_type: 错误类型 (general, execution, initialization, etc.)
            
        Returns:
            格式化的错误消息
        """
        if error_type == "execution":
            return f"❌ **图片生成失败**: {error_message}"
        elif error_type == "initialization":
            return f"❌ **工作流初始化失败**: {error_message}"
        elif error_type == "manager":
            return f"❌ **工作流管理器错误**: {error_message}"
        else:
            return f"❌ **执行工作流时出错**: {error_message}"
    
    def generate_send_error_message(self, error_type: str = "send_failed") -> str:
        """
        生成发送错误消息
        
        Args:
            error_type: 错误类型 (send_failed, processor_not_initialized, etc.)
            
        Returns:
            格式化的发送错误消息
        """
        if error_type == "send_failed":
            return "图片生成成功，但发送到微信失败"
        elif error_type == "processor_not_initialized":
            return "图片生成成功，但图片处理器未初始化"
        else:
            return "图片发送失败"
    
    def _parse_image_type_response(self, session: WorkflowSession, user_response: str) -> Tuple[bool, str]:
        """
        解析用户图片类型回复
        
        Args:
            session: 会话对象
            user_response: 用户回复
            
        Returns:
            (是否成功, 消息)
        """
        user_response = user_response.strip().lower()
        image_count = session.get_image_count()
        
        if image_count == 1:
            # 单图情况
            if user_response in ['1', '控制图', 'control']:
                session.set_image_flux_type(0, FluxImageType.CONTROL)
                return True, "已设置为控制图"
            elif user_response in ['2', '参考图', 'reference']:
                session.set_image_flux_type(0, FluxImageType.REFERENCE)
                return True, "已设置为参考图"
            else:
                return False, "请回复 1（控制图）或 2（参考图）"
        
        elif image_count == 2:
            # 双图情况
            if user_response == '1':
                # 第一张控制图，第二张参考图
                session.set_image_flux_type(0, FluxImageType.CONTROL)
                session.set_image_flux_type(1, FluxImageType.REFERENCE)
                return True, "已设置：第一张控制图，第二张参考图"
            elif user_response == '2':
                # 第一张参考图，第二张控制图
                session.set_image_flux_type(0, FluxImageType.REFERENCE)
                session.set_image_flux_type(1, FluxImageType.CONTROL)
                return True, "已设置：第一张参考图，第二张控制图"
            elif user_response == '3':
                # 两张都是参考图
                session.set_image_flux_type(0, FluxImageType.REFERENCE)
                session.set_image_flux_type(1, FluxImageType.REFERENCE)
                return True, "已设置：两张都是参考图"
            elif user_response == '4':
                # 两张都是控制图
                session.set_image_flux_type(0, FluxImageType.CONTROL)
                session.set_image_flux_type(1, FluxImageType.CONTROL)
                return True, "已设置：两张都是控制图"
            else:
                return False, "请回复 1、2、3 或 4"
        
        else:
            # 多图情况
            if user_response == '取消':
                return False, "已取消，请重新开始"
            
            # 解析逗号分隔的类型列表
            try:
                type_list = [t.strip() for t in user_response.split(',')]
                if len(type_list) != image_count:
                    return False, f"请提供{image_count}个类型，用逗号分隔"
                
                for i, type_str in enumerate(type_list):
                    if type_str in ['控制图', 'control']:
                        session.set_image_flux_type(i, FluxImageType.CONTROL)
                    elif type_str in ['参考图', 'reference']:
                        session.set_image_flux_type(i, FluxImageType.REFERENCE)
                    elif type_str in ['混合', 'mixed']:
                        session.set_image_flux_type(i, FluxImageType.MIXED)
                    else:
                        return False, f"第{i+1}个类型 '{type_str}' 无效，请使用：控制图、参考图、混合"
                
                return True, f"已设置{image_count}张图片的类型"
            except Exception:
                return False, "格式错误，请按顺序回复类型，用逗号分隔"
    
    def check_and_ask_image_types(self, session: WorkflowSession) -> Optional[str]:
        """
        检查是否需要询问图片类型，如果需要则返回询问消息
        
        Args:
            session: 会话对象
            
        Returns:
            询问消息或None
        """
        if not session.needs_image_type_confirmation():
            return None
        
        # 更新状态为等待图片类型确认
        session.state = SessionState.WAITING_FOR_IMAGE_TYPES
        
        # 导入意图分析器生成询问消息
        from ..intent.analyzer import intent_analyzer
        return intent_analyzer.generate_image_type_question(session.get_image_count())


# 全局会话管理器实例
session_manager = SessionManager() 