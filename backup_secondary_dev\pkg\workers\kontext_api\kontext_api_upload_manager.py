"""
图片上传管理器

负责将图片上传到远程 ComfyUI.com 服务器
"""

import aiohttp
import logging
from typing import List, Dict, Any
from .auth_handler import APIAuthHandler


class ImageUploadManager:
    """图片上传管理器"""
    
    def __init__(self, auth_handler: APIAuthHandler):
        self.auth_handler = auth_handler
        self.logger = logging.getLogger(__name__)
        self.upload_url = "https://comfyui.com/api/upload"
        
    async def upload_images(self, images: List[bytes]) -> List[str]:
        """
        上传图片列表
        
        Args:
            images: 图片数据列表
            
        Returns:
            List[str]: 上传后的图片URL列表
        """
        uploaded_urls = []
        
        for i, image_data in enumerate(images):
            try:
                image_url = await self._upload_single_image(image_data, f"image_{i}")
                uploaded_urls.append(image_url)
                self.logger.info(f"图片 {i+1}/{len(images)} 上传成功: {image_url}")
            except Exception as e:
                self.logger.error(f"图片 {i+1} 上传失败: {e}")
                raise
        
        return uploaded_urls
    
    async def _upload_single_image(self, image_data: bytes, filename: str) -> str:
        """上传单张图片"""
        session = await self.auth_handler._get_session()
        
        # 准备multipart表单数据
        data = aiohttp.FormData()
        data.add_field('file', image_data, filename=filename, content_type='image/png')
        
        async with session.post(self.upload_url, data=data) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("url")
            else:
                error_text = await response.text()
                raise Exception(f"上传图片失败: {response.status} - {error_text}")
    
    async def upload_workflow_with_images(
        self, 
        workflow_data: Dict[str, Any], 
        images: List[bytes]
    ) -> Dict[str, Any]:
        """
        上传工作流和图片的组合
        
        Args:
            workflow_data: 工作流数据
            images: 图片数据列表
            
        Returns:
            Dict[str, Any]: 包含图片URL的更新后工作流数据
        """
        # 上传图片
        image_urls = await self.upload_images(images)
        
        # 更新工作流中的图片引用
        updated_workflow = self._update_workflow_image_references(workflow_data, image_urls)
        
        return updated_workflow
    
    def _update_workflow_image_references(
        self, 
        workflow_data: Dict[str, Any], 
        image_urls: List[str]
    ) -> Dict[str, Any]:
        """更新工作流中的图片引用"""
        # 这里需要根据具体的 ComfyUI 工作流格式来更新图片引用
        # 暂时返回原始数据，具体实现需要根据工作流结构
        return workflow_data
    
    async def close(self):
        """关闭上传管理器"""
        # 清理资源
        pass 