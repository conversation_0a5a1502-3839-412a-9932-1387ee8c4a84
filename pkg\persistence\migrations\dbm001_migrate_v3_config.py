from .. import migration
from copy import deepcopy
import uuid
import os
import sqlalchemy
import shutil

from ...config import manager as config_manager
from ...entity.persistence import (
    model as persistence_model,
    pipeline as persistence_pipeline,
    bot as persistence_bot,
)


@migration.migration_class(1)
class DBMigrateV3Config(migration.DBMigration):
    """从 v3 的配置迁移到 v4 的数据库"""

    async def upgrade(self):
        """升级"""
        """
        将 data/config 下的所有配置文件进行迁移。
        迁移后，之前的配置文件都保存到 data/legacy/config 下。
        迁移后，data/metadata/ 下的所有配置文件都保存到 data/legacy/metadata 下。
        """

        if self.ap.provider_cfg is None:
            return

        # ======= 迁移模型 =======
        # 只迁移当前选中的模型
        model_name = self.ap.provider_cfg.data.get('model', 'gpt-4o')

        model_requester = 'openai-chat-completions'
        model_requester_config = {}
        model_api_keys = ['sk-proj-**********']
        model_abilities = []
        model_extra_args = {}

        if os.path.exists('data/metadata/llm-models.json'):
            _llm_model_meta = await config_manager.load_json_config('data/metadata/llm-models.json', completion=False)

            for item in _llm_model_meta.data.get('list', []):
                if item.get('name') == model_name:
                    if 'model_name' in item:
                        model_name = item['model_name']
                    if 'requester' in item:
                        model_requester = item['requester']
                    if 'token_mgr' in item:
                        _token_mgr = item['token_mgr']

                        if _token_mgr in self.ap.provider_cfg.data.get('keys', {}):
                            model_api_keys = self.ap.provider_cfg.data.get('keys', {})[_token_mgr]

                    if 'tool_call_supported' in item and item['tool_call_supported']:
                        model_abilities.append('func_call')

                    if 'vision_supported' in item and item['vision_supported']:
                        model_abilities.append('vision')

                    if (
                        model_requester in self.ap.provider_cfg.data.get('requester', {})
                        and 'args' in self.ap.provider_cfg.data.get('requester', {})[model_requester]
                    ):
                        model_extra_args = self.ap.provider_cfg.data.get('requester', {})[model_requester]['args']

                    if model_requester in self.ap.provider_cfg.data.get('requester', {}):
                        model_requester_config = self.ap.provider_cfg.data.get('requester', {})[model_requester]
                        model_requester_config = {
                            'base_url': model_requester_config['base-url'],
                            'timeout': model_requester_config['timeout'],
                        }

                    break

        model_uuid = str(uuid.uuid4())

        llm_model_data = {
            'uuid': model_uuid,
            'name': model_name,
            'description': '由 LangBot v3 迁移而来',
            'requester': model_requester,
            'requester_config': model_requester_config,
            'api_keys': model_api_keys,
            'abilities': model_abilities,
            'extra_args': model_extra_args,
        }

        await self.ap.persistence_mgr.execute_async(
            sqlalchemy.insert(persistence_model.LLMModel).values(**llm_model_data)
        )

        # ======= 迁移流水线配置 =======
        # 修改到默认流水线
        default_pipeline = [
            self.ap.persistence_mgr.serialize_model(persistence_pipeline.LegacyPipeline, pipeline)
            for pipeline in (
                await self.ap.persistence_mgr.execute_async(
                    sqlalchemy.select(persistence_pipeline.LegacyPipeline).where(
                        persistence_pipeline.LegacyPipeline.is_default == True
                    )
                )
            ).all()
        ][0]

        pipeline_uuid = str(uuid.uuid4())
        pipeline_name = 'ChatPipeline'

        if default_pipeline:
            pipeline_name = default_pipeline['name']
            pipeline_uuid = default_pipeline['uuid']

            pipeline_config = default_pipeline['config']

            # ai
            pipeline_config['ai']['runner'] = {
                'runner': self.ap.provider_cfg.data.get('runner', 'comfyui-agent'),
            }
            pipeline_config['ai']['local-agent']['model'] = model_uuid
            
            # 安全地设置 max-round
            if 'msg-truncate' in self.ap.pipeline_cfg.data and 'round' in self.ap.pipeline_cfg.data['msg-truncate']:
                pipeline_config['ai']['local-agent']['max-round'] = self.ap.pipeline_cfg.data['msg-truncate']['round'].get('max-round', 10)
            else:
                pipeline_config['ai']['local-agent']['max-round'] = 10

            pipeline_config['ai']['local-agent']['prompt'] = [
                {
                    'role': 'system',
                    'content': self.ap.provider_cfg.data.get('prompt', {}).get('default', 'You are a helpful assistant.'),
                }
            ]
            # 安全地设置 dify-service-api 配置
            if 'dify-service-api' in self.ap.provider_cfg.data:
                dify_config = self.ap.provider_cfg.data['dify-service-api']
                pipeline_config['ai']['dify-service-api'] = {
                    'base-url': dify_config.get('base-url', 'https://api.dify.ai/v1'),
                    'app-type': dify_config.get('app-type', 'chat'),
                    'api-key': dify_config.get(dify_config.get('app-type', 'chat'), {}).get('api-key', 'your-api-key'),
                    'thinking-convert': dify_config.get('options', {}).get('convert-thinking-tips', 'plain'),
                    'timeout': dify_config.get(dify_config.get('app-type', 'chat'), {}).get('timeout', 30),
                }
            
            # 安全地设置 dashscope-app-api 配置
            if 'dashscope-app-api' in self.ap.provider_cfg.data:
                dashscope_config = self.ap.provider_cfg.data['dashscope-app-api']
                pipeline_config['ai']['dashscope-app-api'] = {
                    'app-type': dashscope_config.get('app-type', 'agent'),
                    'api-key': dashscope_config.get('api-key', 'your-api-key'),
                    'references_quote': dashscope_config.get(dashscope_config.get('app-type', 'agent'), {}).get('references_quote', '参考资料来自:'),
                }

            # trigger
            # 检查并安全地设置 trigger 配置
            if 'respond-rules' in self.ap.pipeline_cfg.data:
                pipeline_config['trigger']['group-respond-rules'] = self.ap.pipeline_cfg.data['respond-rules']['default']
            elif 'group-respond-rules' in self.ap.pipeline_cfg.data:
                pipeline_config['trigger']['group-respond-rules'] = self.ap.pipeline_cfg.data['group-respond-rules']
            
            if 'access-control' in self.ap.pipeline_cfg.data:
                pipeline_config['trigger']['access-control'] = self.ap.pipeline_cfg.data['access-control']
            
            if 'ignore-rules' in self.ap.pipeline_cfg.data:
                pipeline_config['trigger']['ignore-rules'] = self.ap.pipeline_cfg.data['ignore-rules']

            # safety
            pipeline_config['safety']['content-filter'] = {
                'scope': 'all',
                'check-sensitive-words': self.ap.pipeline_cfg.data.get('check-sensitive-words', True),
            }
            
            # 安全地设置 rate-limit 配置
            if 'rate-limit' in self.ap.pipeline_cfg.data:
                rate_limit_config = self.ap.pipeline_cfg.data['rate-limit']
                pipeline_config['safety']['rate-limit'] = {
                    'window-length': rate_limit_config.get('fixwin', {}).get('default', {}).get('window-size', 60),
                    'limitation': rate_limit_config.get('fixwin', {}).get('default', {}).get('limit', 60),
                    'strategy': rate_limit_config.get('strategy', 'drop'),
                }
            else:
                pipeline_config['safety']['rate-limit'] = {
                    'window-length': 60,
                    'limitation': 60,
                    'strategy': 'drop',
                }

            # output
            # 安全地设置 output 配置
            if self.ap.platform_cfg and self.ap.platform_cfg.data:
                platform_data = self.ap.platform_cfg.data
                pipeline_config['output']['long-text-processing'] = platform_data.get('long-text-process', {
                    'threshold': 1000,
                    'strategy': 'forward',
                    'font-path': ''
                })
                pipeline_config['output']['force-delay'] = platform_data.get('force-delay', {
                    'min': 0,
                    'max': 0
                })
                pipeline_config['output']['misc'] = {
                    'hide-exception': platform_data.get('hide-exception-info', True),
                    'quote-origin': platform_data.get('quote-origin', True),
                    'at-sender': platform_data.get('at-sender', True),
                    'track-function-calls': platform_data.get('track-function-calls', False),
                }
            else:
                # 使用默认值
                pipeline_config['output']['long-text-processing'] = {
                    'threshold': 1000,
                    'strategy': 'forward',
                    'font-path': ''
                }
                pipeline_config['output']['force-delay'] = {
                    'min': 0,
                    'max': 0
                }
                pipeline_config['output']['misc'] = {
                    'hide-exception': True,
                    'quote-origin': True,
                    'at-sender': True,
                    'track-function-calls': False,
                }

            default_pipeline['description'] = default_pipeline['description'] + ' [已迁移 LangBot v3 配置]'
            default_pipeline['config'] = pipeline_config
            default_pipeline.pop('created_at')
            default_pipeline.pop('updated_at')

            await self.ap.persistence_mgr.execute_async(
                sqlalchemy.update(persistence_pipeline.LegacyPipeline)
                .values(default_pipeline)
                .where(persistence_pipeline.LegacyPipeline.uuid == default_pipeline['uuid'])
            )

        # ======= 迁移机器人 =======
        # 只迁移启用的机器人
        if self.ap.platform_cfg and self.ap.platform_cfg.data:
            for adapter in self.ap.platform_cfg.data.get('platform-adapters', []):
                if not adapter.get('enable'):
                    continue

                args = deepcopy(adapter)
                args.pop('adapter')
                args.pop('enable')

                bot_data = {
                    'uuid': str(uuid.uuid4()),
                    'name': adapter.get('adapter'),
                    'description': '由 LangBot v3 迁移而来',
                    'adapter': adapter.get('adapter'),
                    'adapter_config': args,
                    'enable': True,
                    'use_pipeline_uuid': pipeline_uuid,
                    'use_pipeline_name': pipeline_name,
                }

                await self.ap.persistence_mgr.execute_async(sqlalchemy.insert(persistence_bot.Bot).values(**bot_data))

        # ======= 迁移系统设置 =======
        # 安全地迁移系统设置
        if self.ap.system_cfg and self.ap.system_cfg.data:
            system_data = self.ap.system_cfg.data
            self.ap.instance_config.data['admins'] = system_data.get('admin-sessions', [])
            self.ap.instance_config.data['api']['port'] = system_data.get('http-api', {}).get('port', 8080)
            self.ap.instance_config.data['concurrency']['pipeline'] = system_data.get('pipeline-concurrency', 1)
            self.ap.instance_config.data['concurrency']['session'] = system_data.get('session-concurrency', {}).get('default', 1)
            self.ap.instance_config.data['proxy'] = system_data.get('network-proxies', {})
        
        if self.ap.command_cfg and self.ap.command_cfg.data:
            command_data = self.ap.command_cfg.data
            self.ap.instance_config.data['command'] = {
                'prefix': command_data.get('command-prefix', '/'),
                'privilege': command_data.get('privilege', {}),
            }
        
        if self.ap.provider_cfg and self.ap.provider_cfg.data:
            self.ap.instance_config.data['mcp'] = self.ap.provider_cfg.data.get('mcp', {})
        
        await self.ap.instance_config.dump_config()

        # ======= move files =======
        # 迁移 data/config 下的所有配置文件
        all_legacy_dir_name = [
            'config',
            # 'metadata',
            'prompts',
            'scenario',
        ]

        def move_legacy_files(dir_name: str):
            if not os.path.exists(f'data/legacy/{dir_name}'):
                os.makedirs(f'data/legacy/{dir_name}')

            if os.path.exists(f'data/{dir_name}'):
                for file in os.listdir(f'data/{dir_name}'):
                    if file.endswith('.json'):
                        shutil.move(f'data/{dir_name}/{file}', f'data/legacy/{dir_name}/{file}')

                os.rmdir(f'data/{dir_name}')

        for dir_name in all_legacy_dir_name:
            move_legacy_files(dir_name)

    async def downgrade(self):
        """降级"""
