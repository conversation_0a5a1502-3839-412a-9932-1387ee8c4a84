from __future__ import annotations

"""
[二次开发] 标准图片处理器
负责处理图片相关的辅助功能

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：将生成的图片发送到微信，创建图片消息
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：ComfyUI图片生成工作流

功能边界：
- 将生成的图片发送到微信
- 创建图片消息
- 不负责工作流执行（由flux_workflow_manager负责）
- 不负责参数分析（由统一路由系统负责）

[迁移说明] 此文件已从 pkg/provider/runners/ 迁移到 pkg/workers/shared/image_handlers/
原因：优化架构层级，将工具类与运行器分离
迁移时间：2025-01-09
"""

import base64
from typing import Optional

from ....provider import entities as llm_entities
from ....core import app, entities as core_entities
from ..image_senders import WeChatImageSender


class StandardImageHandler:
    """标准图片处理器
    
    功能边界：
    - 将生成的图片发送到微信
    - 创建图片消息
    - 不负责工作流执行（由flux_workflow_manager负责）
    - 不负责参数分析（由统一路由系统负责）
    """
    
    def __init__(self, ap: app.Application, pipeline_config: dict):
        self.ap = ap
        self.pipeline_config = pipeline_config

        # 初始化共享的微信图片发送器
        self.wechat_sender = WeChatImageSender(logger=self.ap.logger)

        # 添加种子记录，用于"使用上一次种子"功能
        self.last_seed = None
        self.seed_history = []  # 记录最近的种子历史

    async def create_image_message(self, image_data: bytes) -> Optional[llm_entities.Message]:
        """创建图片消息（仅创建，不发送）"""
        try:
            # 将图片数据转换为 base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 创建图片内容元素
            image_content = llm_entities.ContentElement.from_image_base64(image_base64)
            
            # 创建消息
            message = llm_entities.Message(
                role='assistant',
                content=[image_content]
            )
            
            return message
            
        except Exception as e:
            self.ap.logger.error(f"创建图片消息出错: {str(e)}")
            return None

    async def send_image_to_wechat(self, image_data: bytes, query: core_entities.Query) -> bool:
        """发送图片到微信 - 使用共享的发送器"""
        return await self.wechat_sender.send_image_to_wechat(image_data, query)