#!/usr/bin/env python3
"""
LangBot 配置恢复脚本
用于恢复基本的机器人、流水线和模型配置
"""

import sqlite3
import json
import uuid
from datetime import datetime

def restore_basic_config():
    """恢复基本配置"""
    db_path = "data/langbot.db"
    
    # 连接到数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 恢复 WeChatPad 机器人配置
        print("🔧 恢复 WeChatPad 机器人配置...")
        
        # 检查是否已存在机器人
        cursor.execute("SELECT COUNT(*) FROM bots WHERE adapter = 'wechatpad'")
        if cursor.fetchone()[0] == 0:
            bot_uuid = str(uuid.uuid4())
            bot_config = {
                'uuid': bot_uuid,
                'name': 'RA_Jarvis',
                'description': 'WeChatPad 机器人 - 自动恢复',
                'adapter': 'wechatpad',
                'adapter_config': json.dumps({
                    'api_url': 'http://localhost:8080',
                    'token': 'your-token-here',  # 需要用户手动填写
                    'timeout': 30
                }),
                'enable': True,
                'use_pipeline_uuid': '',  # 稍后设置
                'use_pipeline_name': 'ChatPipeline',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            cursor.execute("""
                INSERT INTO bots (uuid, name, description, adapter, adapter_config, enable, use_pipeline_uuid, use_pipeline_name, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                bot_config['uuid'], bot_config['name'], bot_config['description'],
                bot_config['adapter'], bot_config['adapter_config'], bot_config['enable'],
                bot_config['use_pipeline_uuid'], bot_config['use_pipeline_name'],
                bot_config['created_at'], bot_config['updated_at']
            ))
            print("✅ WeChatPad 机器人配置已恢复")
        else:
            print("ℹ️  WeChatPad 机器人已存在，跳过")
        
        # 2. 恢复 ComfyUI 流水线配置
        print("🔧 恢复 ComfyUI 流水线配置...")
        
        # 检查是否已存在流水线
        cursor.execute("SELECT COUNT(*) FROM legacy_pipelines WHERE name = 'ChatPipeline'")
        if cursor.fetchone()[0] == 0:
            pipeline_uuid = str(uuid.uuid4())
            
            # 从 legacy 配置读取
            with open('data/legacy/config/pipeline.json', 'r', encoding='utf-8') as f:
                pipeline_config = json.load(f)
            
            # 更新配置
            pipeline_config['ai']['comfyui-agent']['api-url'] = 'http://localhost:8188'
            pipeline_config['ai']['comfyui-agent']['default-workflow'] = 'flux_default.json'
            
            pipeline_data = {
                'uuid': pipeline_uuid,
                'name': 'ChatPipeline',
                'description': 'ComfyUI 工作流流水线 - 自动恢复',
                'config': json.dumps(pipeline_config),
                'is_default': True,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            cursor.execute("""
                INSERT INTO legacy_pipelines (uuid, name, description, config, is_default, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                pipeline_data['uuid'], pipeline_data['name'], pipeline_data['description'],
                pipeline_data['config'], pipeline_data['is_default'],
                pipeline_data['created_at'], pipeline_data['updated_at']
            ))
            print("✅ ComfyUI 流水线配置已恢复")
            
            # 更新机器人的流水线引用
            cursor.execute("""
                UPDATE bots SET use_pipeline_uuid = ?, use_pipeline_name = ?
                WHERE adapter = 'wechatpad'
            """, (pipeline_uuid, 'ChatPipeline'))
            
        else:
            print("ℹ️  ComfyUI 流水线已存在，跳过")
        
        # 3. 恢复基本模型配置
        print("🔧 恢复基本模型配置...")
        
        # 检查是否已存在模型
        cursor.execute("SELECT COUNT(*) FROM llm_models WHERE name = 'gpt-4o'")
        if cursor.fetchone()[0] == 0:
            model_uuid = str(uuid.uuid4())
            model_data = {
                'uuid': model_uuid,
                'name': 'gpt-4o',
                'description': 'OpenAI GPT-4o 模型 - 自动恢复',
                'requester': 'openai-chat-completions',
                'requester_config': json.dumps({
                    'base_url': 'https://api.openai.com/v1',
                    'timeout': 30
                }),
                'api_keys': json.dumps({
                    'openai': 'your-openai-api-key-here'  # 需要用户手动填写
                }),
                'abilities': json.dumps(['func_call', 'vision']),
                'extra_args': json.dumps({}),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            cursor.execute("""
                INSERT INTO llm_models (uuid, name, description, requester, requester_config, api_keys, abilities, extra_args, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                model_data['uuid'], model_data['name'], model_data['description'],
                model_data['requester'], model_data['requester_config'], model_data['api_keys'],
                model_data['abilities'], model_data['extra_args'],
                model_data['created_at'], model_data['updated_at']
            ))
            print("✅ GPT-4o 模型配置已恢复")
        else:
            print("ℹ️  GPT-4o 模型已存在，跳过")
        
        # 提交更改
        conn.commit()
        print("\n🎉 基本配置恢复完成！")
        print("\n📝 接下来需要手动配置的内容：")
        print("1. 在 WebUI 中设置 WeChatPad 的 token")
        print("2. 在 WebUI 中设置 OpenAI API Key")
        print("3. 确认 ComfyUI 服务地址是否正确")
        print("4. 根据需要调整流水线配置")
        
    except Exception as e:
        print(f"❌ 恢复配置时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 开始恢复 LangBot 基本配置...")
    restore_basic_config() 