"""
消息去重和时效性检查模块
防止重连时积压消息重复处理
"""

import time
import hashlib
import json
from typing import Dict, Set, Optional, Any
from dataclasses import dataclass
import logging


@dataclass
class MessageInfo:
    """消息信息"""
    message_id: str
    content_hash: str
    timestamp: float
    user_id: str
    chat_id: str
    content: str


class MessageDeduplication:
    """消息去重器"""
    
    def __init__(self, 
                 message_ttl: int = 3600,  # 消息有效期1小时
                 startup_grace_period: int = 300,  # 启动后5分钟内忽略积压消息
                 max_cache_size: int = 10000):
        self.logger = logging.getLogger(__name__)
        self.message_ttl = message_ttl
        self.startup_grace_period = startup_grace_period
        self.max_cache_size = max_cache_size
        
        # 消息缓存：content_hash -> MessageInfo
        self.message_cache: Dict[str, MessageInfo] = {}
        # 用户最后活跃时间：user_id -> timestamp
        self.user_last_activity: Dict[str, float] = {}
        # 系统启动时间
        self.startup_time = time.time()
        
        self.logger.info(f"消息去重器启动: TTL={message_ttl}s, 启动保护期={startup_grace_period}s")
    
    def should_process_message(self, 
                             user_id: str, 
                             chat_id: str, 
                             content: str, 
                             message_id: Optional[str] = None) -> bool:
        """
        判断是否应该处理消息
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            content: 消息内容
            message_id: 消息ID（可选）
            
        Returns:
            bool: True表示应该处理，False表示应该忽略
        """
        current_time = time.time()
        
        # 1. 启动保护期检查
        if self._is_in_startup_grace_period(current_time):
            self.logger.warning(f"启动保护期内忽略消息: {content[:50]}...")
            return False
        
        # 2. 生成消息标识
        content_hash = self._generate_content_hash(user_id, content)
        
        # 3. 检查重复消息
        if self._is_duplicate_message(content_hash, current_time):
            self.logger.warning(f"检测到重复消息，忽略: {content[:50]}...")
            return False
        
        # 4. 检查用户活跃度（防止批量重发）
        if self._is_batch_resend(user_id, current_time):
            self.logger.warning(f"检测到用户 {user_id} 批量重发，忽略: {content[:50]}...")
            return False
        
        # 5. 记录消息
        self._record_message(content_hash, user_id, chat_id, content, current_time, message_id)
        
        # 6. 清理过期缓存
        self._cleanup_expired_messages(current_time)
        
        return True
    
    def _is_in_startup_grace_period(self, current_time: float) -> bool:
        """检查是否在启动保护期内"""
        return (current_time - self.startup_time) < self.startup_grace_period
    
    def _generate_content_hash(self, user_id: str, content: str) -> str:
        """生成消息内容哈希"""
        # 包含用户ID和内容，防止不同用户的相同内容被误判为重复
        raw_data = f"{user_id}:{content.strip()}"
        return hashlib.md5(raw_data.encode('utf-8')).hexdigest()
    
    def _is_duplicate_message(self, content_hash: str, current_time: float) -> bool:
        """检查是否为重复消息"""
        if content_hash in self.message_cache:
            message_info = self.message_cache[content_hash]
            # 检查是否在TTL内
            if (current_time - message_info.timestamp) < self.message_ttl:
                return True
            else:
                # 过期的重复消息，删除缓存
                del self.message_cache[content_hash]
        return False
    
    def _is_batch_resend(self, user_id: str, current_time: float) -> bool:
        """检测批量重发（同一用户短时间内发送多条消息）"""
        last_activity = self.user_last_activity.get(user_id, 0)
        
        # 检查最近的消息频率
        recent_messages = [
            msg for msg in self.message_cache.values() 
            if msg.user_id == user_id and (current_time - msg.timestamp) < 10
        ]
        
        # 只有在短时间内有大量消息（超过5条）才判断为批量重发
        # 这样避免误判正常的对话
        is_batch = len(recent_messages) > 5  # 10秒内超过5条消息才判断为批量
        
        if is_batch:
            self.logger.warning(f"用户 {user_id} 在10秒内发送了 {len(recent_messages)} 条消息，判断为批量重发")
        
        # 更新用户活跃时间
        self.user_last_activity[user_id] = current_time
        return is_batch
    
    def _record_message(self, content_hash: str, user_id: str, chat_id: str, 
                       content: str, timestamp: float, message_id: Optional[str]):
        """记录消息信息"""
        message_info = MessageInfo(
            message_id=message_id or f"auto_{int(timestamp)}",
            content_hash=content_hash,
            timestamp=timestamp,
            user_id=user_id,
            chat_id=chat_id,
            content=content[:100]  # 只保存前100字符用于调试
        )
        
        self.message_cache[content_hash] = message_info
        
        # 限制缓存大小
        if len(self.message_cache) > self.max_cache_size:
            self._cleanup_oldest_messages()
    
    def _cleanup_expired_messages(self, current_time: float):
        """清理过期消息"""
        expired_hashes = [
            content_hash for content_hash, message_info in self.message_cache.items()
            if (current_time - message_info.timestamp) > self.message_ttl
        ]
        
        for content_hash in expired_hashes:
            del self.message_cache[content_hash]
        
        if expired_hashes:
            self.logger.debug(f"清理了 {len(expired_hashes)} 条过期消息")
    
    def _cleanup_oldest_messages(self):
        """清理最旧的消息（当缓存满时）"""
        if len(self.message_cache) <= self.max_cache_size:
            return
        
        # 按时间排序，删除最旧的消息
        sorted_messages = sorted(
            self.message_cache.items(), 
            key=lambda x: x[1].timestamp
        )
        
        # 删除最旧的20%
        delete_count = len(sorted_messages) // 5
        for i in range(delete_count):
            content_hash = sorted_messages[i][0]
            del self.message_cache[content_hash]
        
        self.logger.debug(f"清理了 {delete_count} 条最旧消息")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = time.time()
        return {
            'total_cached_messages': len(self.message_cache),
            'active_users': len(self.user_last_activity),
            'startup_time': self.startup_time,
            'uptime_seconds': current_time - self.startup_time,
            'in_startup_grace_period': self._is_in_startup_grace_period(current_time),
            'cache_size_limit': self.max_cache_size,
            'message_ttl': self.message_ttl
        }


# 全局单例
_message_deduplication: Optional[MessageDeduplication] = None


def get_message_deduplication(
    message_ttl: int = 3600,
    startup_grace_period: int = 300,
    max_cache_size: int = 10000
) -> MessageDeduplication:
    """获取消息去重器单例"""
    global _message_deduplication
    if _message_deduplication is None:
        _message_deduplication = MessageDeduplication(
            message_ttl, startup_grace_period, max_cache_size
        )
    return _message_deduplication


def clear_message_deduplication():
    """清理全局消息去重器实例"""
    global _message_deduplication
    _message_deduplication = None 