"""
Civitai API客户端
用于搜索、下载和管理Civitai上的LoRA模型
"""

import asyncio
import aiohttp
import aiofiles
import os
import json
import yaml
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import logging


@dataclass
class CivitaiModel:
    """Civitai模型信息"""
    id: int
    name: str
    description: str
    type: str
    nsfw: bool
    tags: List[str]
    creator: str
    stats: Dict[str, Any]
    model_versions: List[Dict[str, Any]]
    
    @property
    def rating(self) -> float:
        """获取评分"""
        return self.stats.get('rating', 0.0)
    
    @property
    def download_count(self) -> int:
        """获取下载次数"""
        return self.stats.get('downloadCount', 0)
    
    @property
    def latest_version(self) -> Optional[Dict[str, Any]]:
        """获取最新版本"""
        return self.model_versions[0] if self.model_versions else None


class CivitaiClient:
    """Civitai API客户端"""
    
    def __init__(self, config_path: str = "config/civitai_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = logging.getLogger(__name__)
        
        # API配置
        self.base_url = self.config.get('base_url', 'https://civitai.com/api/v1')
        self.api_key = self.config.get('api_key')
        self.proxy = self.config.get('proxy')
        self.timeout = self.config.get('timeout', 30)
        self.download_dir = self.config.get('download_dir', '/home/<USER>/Workspace/ComfyUI/models/loras')
        
        # 确保下载目录存在
        os.makedirs(self.download_dir, exist_ok=True)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except FileNotFoundError:
            self.logger.warning(f"配置文件不存在: {self.config_path}")
            return {}
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    async def search_models(self, 
                          query: str = "", 
                          types: str = "LORA",
                          sort: str = "Highest Rated",
                          limit: int = 20,
                          nsfw: bool = False) -> List[CivitaiModel]:
        """搜索模型"""
        try:
            params = {
                'limit': limit,
                'types': types,
                'sort': sort,
                'nsfw': nsfw
            }
            
            if query:
                params['query'] = query
            
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            connector = None
            if self.proxy:
                connector = aiohttp.TCPConnector()
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=headers
            ) as session:
                
                # 设置代理
                proxy_url = self.proxy if self.proxy else None
                
                url = f"{self.base_url}/models"
                
                async with session.get(url, params=params, proxy=proxy_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = []
                        
                        for item in data.get('items', []):
                            model = CivitaiModel(
                                id=item['id'],
                                name=item['name'],
                                description=item.get('description', ''),
                                type=item['type'],
                                nsfw=item.get('nsfw', False),
                                tags=item.get('tags', []),
                                creator=item.get('creator', {}).get('username', ''),
                                stats=item.get('stats', {}),
                                model_versions=item.get('modelVersions', [])
                            )
                            models.append(model)
                        
                        self.logger.info(f"搜索到 {len(models)} 个模型")
                        return models
                    else:
                        self.logger.error(f"搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"搜索模型失败: {e}")
            return []
    
    async def get_model_details(self, model_id: int) -> Optional[CivitaiModel]:
        """获取模型详细信息"""
        try:
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            connector = None
            if self.proxy:
                connector = aiohttp.TCPConnector()
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=headers
            ) as session:
                
                proxy_url = self.proxy if self.proxy else None
                url = f"{self.base_url}/models/{model_id}"
                
                async with session.get(url, proxy=proxy_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        model = CivitaiModel(
                            id=data['id'],
                            name=data['name'],
                            description=data.get('description', ''),
                            type=data['type'],
                            nsfw=data.get('nsfw', False),
                            tags=data.get('tags', []),
                            creator=data.get('creator', {}).get('username', ''),
                            stats=data.get('stats', {}),
                            model_versions=data.get('modelVersions', [])
                        )
                        
                        return model
                    else:
                        self.logger.error(f"获取模型详情失败: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"获取模型详情失败: {e}")
            return None
    
    async def download_model(self, model: CivitaiModel, version_id: Optional[int] = None) -> Optional[str]:
        """下载模型文件"""
        try:
            # 选择版本
            if version_id:
                version = next((v for v in model.model_versions if v['id'] == version_id), None)
            else:
                version = model.latest_version
            
            if not version:
                self.logger.error("没有找到可下载的版本")
                return None
            
            # 获取下载文件
            files = version.get('files', [])
            if not files:
                self.logger.error("没有找到可下载的文件")
                return None
            
            # 选择主要文件（通常是第一个）
            file_info = files[0]
            download_url = file_info['downloadUrl']
            filename = file_info['name']
            
            # 构建本地文件路径
            local_path = os.path.join(self.download_dir, filename)
            
            # 检查文件是否已存在
            if os.path.exists(local_path):
                self.logger.info(f"文件已存在: {local_path}")
                return local_path
            
            # 下载文件
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            connector = None
            if self.proxy:
                connector = aiohttp.TCPConnector()
            
            timeout = aiohttp.ClientTimeout(total=self.config.get('download_timeout', 300))
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=headers
            ) as session:
                
                proxy_url = self.proxy if self.proxy else None
                
                async with session.get(download_url, proxy=proxy_url) as response:
                    if response.status == 200:
                        total_size = int(response.headers.get('content-length', 0))
                        downloaded = 0
                        
                        async with aiofiles.open(local_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(self.config.get('chunk_size', 8192)):
                                await f.write(chunk)
                                downloaded += len(chunk)
                                
                                # 显示进度
                                if total_size > 0:
                                    progress = (downloaded / total_size) * 100
                                    if downloaded % (1024 * 1024) == 0:  # 每MB显示一次
                                        self.logger.info(f"下载进度: {progress:.1f}%")
                        
                        self.logger.info(f"下载完成: {local_path}")
                        return local_path
                    else:
                        self.logger.error(f"下载失败: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"下载模型失败: {e}")
            return None
    
    def check_model_status(self, filename: str) -> Dict[str, Any]:
        """检查本地模型状态"""
        local_path = os.path.join(self.download_dir, filename)
        
        status = {
            'exists': os.path.exists(local_path),
            'path': local_path,
            'size': 0,
            'readable': False
        }
        
        if status['exists']:
            try:
                stat = os.stat(local_path)
                status['size'] = stat.st_size
                status['readable'] = os.access(local_path, os.R_OK)
                status['modified_time'] = stat.st_mtime
            except Exception as e:
                self.logger.error(f"检查文件状态失败: {e}")
        
        return status


# 全局实例
civitai_client = CivitaiClient()
