"""
[二次开发] Kontext 提示词优化模块
负责LLM润色、提示词增强等

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext工作流的提示词优化和LLM翻译
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：REQ-01 需求范围定义
- 依赖关系：依赖langbot的LLM模型管理器和查询对象
"""
import re
import logging
from typing import Dict, Optional, Any

class KontextPromptOptimizer:
    """
    LLM提示词优化与增强
    """
    def __init__(self, lora_dict: Optional[Dict[str, str]] = None):
        self.lora_dict = lora_dict or {}
        self.logger = logging.getLogger(__name__)

    def optimize_prompt(self, prompt: str) -> str:
        """
        对输入的prompt进行优化（如润色、去噪、增强等）
        """
        if not prompt or not prompt.strip():
            return prompt

        # 基础清理
        cleaned_prompt = self._clean_prompt(prompt)

        # 简单的英文优化（如果是中文，提示用户使用英文）
        if self._is_chinese(cleaned_prompt):
            # 如果是中文，提示用户LLM翻译不可用
            self.logger.warning("检测到中文提示词，但LLM翻译不可用")
            return self._prompt_for_english(cleaned_prompt)

        # 英文提示词优化
        optimized = self._enhance_english_prompt(cleaned_prompt)
        return optimized

    def _clean_prompt(self, prompt: str) -> str:
        """清理提示词：保留中文标点，仅清理英文特殊符号"""
        cleaned = prompt.strip()
        # 检查是否包含中文
        if self._is_chinese(cleaned):
            # 仅移除英文特殊符号，保留中文标点
            # 中文标点范围：\u3000-\u303F，\uFF00-\uFFEF
            cleaned = re.sub(r'[A-Za-z0-9@#\$%\^&\*_+=<>\|~`]', '', cleaned)
            # 不移除中文逗号、句号、顿号等
        else:
            # 英文场景下移除特殊字符（保留基本标点）
            cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\']', '', cleaned)
        return cleaned

    def _is_chinese(self, text: str) -> bool:
        """检测是否包含中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))

    def _enhance_english_prompt(self, prompt: str) -> str:
        """增强英文提示词"""
        # 基础艺术风格增强
        enhanced = prompt

        # 如果没有质量词，添加基础质量描述
        quality_keywords = ['high quality', 'detailed', 'masterpiece', 'best quality']
        if not any(keyword in enhanced.lower() for keyword in quality_keywords):
            enhanced = f"high quality, detailed, {enhanced}"

        # 确保语法正确
        enhanced = enhanced.strip()
        if not enhanced.endswith('.'):
            enhanced += '.'

        return enhanced

    async def optimize_prompt_with_llm(self, prompt: str, query: Any, no_trans: bool = False) -> str:
        """
        使用LLM优化提示词（完整版本）

        Args:
            prompt: 用户提示词
            query: 查询对象
            no_trans: 是否禁用润色，仅简单翻译
        """
        try:
            # 检测语言
            if self._is_chinese(prompt):
                # 中文需要翻译和优化
                return await self._translate_and_optimize(prompt, query, no_trans)
            else:
                # 英文直接优化（如果no_trans为True则跳过优化）
                if no_trans:
                    return prompt  # 直接返回原始英文
                else:
                    return await self._optimize_english_with_llm(prompt, query)
        except Exception as e:
            self.logger.error(f"LLM提示词优化失败: {e}")
            # 不使用回退机制，直接抛出异常
            raise Exception(f"LLM提示词优化失败: {str(e)}")

    async def _translate_and_optimize(self, chinese_prompt: str, query: Any, no_trans: bool = False) -> str:
        """
        翻译中文并优化（Kontext专用系统提示词，采用自然语言+专业修饰词最佳实践）

        Args:
            chinese_prompt: 中文提示词
            query: 查询对象
            no_trans: 是否禁用润色，仅简单翻译
        """
        try:
            if no_trans:
                # 简单翻译模式：直接翻译，不润色
                system_prompt = '''You are a precise Chinese-to-English translator. Translate the user's description directly and accurately.

**CRITICAL REQUIREMENTS:**
1. Translate literally and precisely
2. Keep specific time references exact (下午6点 → 6 PM, 黄昏 → dusk, 傍晚 → evening)
3. Do NOT add descriptive words or enhancements
4. Return ONLY the translation, no explanations
5. No titles, prefixes, or markdown formatting

**Examples:**
1. Input: 更改这张图片为下午6点的光线
   Output: Change this image to 6 PM lighting.

2. Input: 把这张图变成黄昏效果
   Output: Convert this image to dusk effect.

3. Input: 让人物看起来更年轻
   Output: Make the person look younger.'''
            else:
                # 润色模式：翻译并适度增强
                system_prompt = '''You are an AI image editing prompt optimizer. Convert the user's description into clear English instruction with a bit more detail.

**CRITICAL REQUIREMENTS:**
1. Keep output under 65 words (CLIP model has 77 token limit)
2. Add 1-2 descriptive words for better visual quality
3. Keep the main instruction simple and direct
4. Preserve specific time references literally (下午6点 → 6 PM, 上午10点 → 10 AM)
5. Return ONLY the instruction text, no explanations
6. No titles, prefixes, or markdown formatting

**Examples:**
1. Input: 将这张建筑照片转换为夜景效果，增加灯光反射
   Output: Transform to night scene with enhanced glass reflections and warm lighting.

2. Input: 把这张图变成水彩画效果
   Output: Convert to watercolor painting style with soft brushstrokes, maintain structure.

3. Input: 让人物看起来更年轻，肤色更明亮
   Output: Make person appear younger with brighter, smoother skin tones.

4. Input: 更改这张图片为下午6点光线，建筑主体结构不变
   Output: Change to 6 PM lighting with warm afternoon atmosphere, preserve architectural structure.

5. Input: 调整为傍晚时分的氛围
   Output: Adjust to evening twilight atmosphere with soft purple and orange hues.'''
            user_prompt = f"Please optimize this user prompt: {chinese_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                # 清理响应，确保只返回英文提示词
                cleaned_prompt = self._clean_llm_response(optimized)
                return cleaned_prompt
            else:
                # LLM返回空响应时，直接报错
                raise Exception("LLM返回空响应，提示词优化失败")
        except Exception as e:
            self.logger.error(f"翻译优化失败: {e}")
            # 异常时直接抛出，不使用回退机制
            raise Exception(f"LLM提示词优化失败: {str(e)}")

    async def _optimize_english_with_llm(self, english_prompt: str, query: Any) -> str:
        """
        优化英文提示词（Kontext专用系统提示词，采用自然语言+专业修饰词最佳实践）
        """
        try:
            system_prompt = '''You are an AI image editing prompt optimizer. Enhance the user's instruction with a bit more descriptive detail.

**CRITICAL REQUIREMENTS:**
1. Keep output under 65 words (CLIP model has 77 token limit)
2. Add 1-2 descriptive words for better visual quality
3. Keep the main instruction simple and direct
4. Return ONLY the instruction text, no explanations
5. No titles, prefixes, or markdown formatting

**Examples:**
1. Input: Transform this architectural photo into a vibrant night scene with enhanced reflections
   Output: Transform to vibrant night scene with enhanced glass reflections and warm lighting.

2. Input: Convert the image into a watercolor painting style with soft artistic touch
   Output: Convert to watercolor painting style with soft brushstrokes and artistic texture.'''
            user_prompt = f"Please optimize this user prompt: {english_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                # 清理响应，确保只返回英文提示词
                cleaned_prompt = self._clean_llm_response(optimized)
                return cleaned_prompt
            else:
                raise Exception("LLM返回空响应，英文提示词优化失败")
        except Exception as e:
            self.logger.error(f"英文优化失败: {e}")
            raise Exception(f"LLM英文提示词优化失败: {str(e)}")

    async def _call_llm_for_optimization(self, system_prompt: str, user_prompt: str, query: Any) -> Optional[str]:
        """调用LLM进行优化"""
        try:
            # 检查query对象
            if not query or not hasattr(query, 'pipeline_config') or not query.pipeline_config:
                self.logger.warning("无效的query对象，无法调用LLM")
                return None

            # 获取LLM模型配置 - 支持多种runner类型
            ai_config = query.pipeline_config.get('ai', {})
            runner_type = ai_config.get('runner', {}).get('runner', 'local-agent')
            self.logger.info(f"🔍 检测到runner类型: {runner_type}")

            # 🔥 添加详细的配置调试信息
            self.logger.info(f"🔍 完整ai配置: {ai_config}")
            runner_config = ai_config.get(runner_type, {})
            self.logger.info(f"🔍 {runner_type}配置: {runner_config}")

            model_uuid = runner_config.get('model', '')
            self.logger.info(f"🔍 获取到的模型UUID: '{model_uuid}'")

            if not model_uuid:
                self.logger.warning(f"❌ 未在{runner_type}配置中找到LLM模型，无法进行提示词优化")
                self.logger.info(f"🔍 可用的配置键: {list(runner_config.keys())}")
                return None

            self.logger.info(f"✅ 找到模型UUID: {model_uuid}")

            # 获取应用实例
            from ...core.workflow.query_utils import safe_get_query_ap
            ap = safe_get_query_ap(query, self.logger)
            if not ap:
                self.logger.warning("❌ 无法获取应用实例，无法调用LLM")
                return None

            self.logger.info(f"✅ 应用实例存在，model_mgr: {ap.model_mgr is not None}")

            # 查找对应的RuntimeLLMModel
            runtime_llm_model = None
            available_models = []
            for model in ap.model_mgr.llm_models:
                available_models.append(model.model_entity.uuid)
                if model.model_entity.uuid == model_uuid:
                    runtime_llm_model = model
                    break

            self.logger.info(f"🔍 可用的LLM模型: {available_models}")

            if not runtime_llm_model:
                self.logger.warning(f"❌ 未找到模型 {model_uuid}，无法进行提示词优化")
                self.logger.info(f"🔍 总共有 {len(query.ap.model_mgr.llm_models)} 个可用模型")
                return None

            self.logger.info(f"✅ 找到匹配的LLM模型: {runtime_llm_model.model_entity.name}")

            # 导入必要的模块
            from ...provider import entities as llm_entities

            # 创建消息
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]

            # 设置LLM参数以提高翻译精确性
            extra_args = {
                'temperature': 0.3,  # 降低随机性，提高时间翻译的精确性
                'max_tokens': 100,   # 严格限制输出长度，确保不超过CLIP token限制
                'top_p': 0.8,        # 核采样参数，更保守的选择
            }

            # 调用LLM
            self.logger.info(f"🔍 [DEBUG] 开始调用LLM，系统提示词长度: {len(system_prompt)}")
            self.logger.info(f"🔍 [DEBUG] 用户提示词: {user_prompt}")

            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args=extra_args,
            )

            # 提取响应文本
            response_text = self._extract_response_text(result)
            self.logger.info(f"🔍 [DEBUG] LLM原始响应: {response_text}")

            if response_text:
                self.logger.info("LLM提示词优化成功")
                return response_text.strip()
            else:
                self.logger.warning("LLM返回空响应")
                return None

        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            return None

    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本"""
        response_text = ""

        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)

        return response_text.strip()

    def _clean_llm_response(self, response: str) -> str:
        """清理LLM响应，提取纯净的英文提示词"""
        if not response:
            return response

        # 移除常见的解释性前缀和后缀
        response = response.strip()

        # 如果包含中文，尝试提取英文部分
        if self._is_chinese(response):
            # 查找引号内的英文内容
            import re
            english_matches = re.findall(r'"([^"]*)"', response)
            for match in english_matches:
                if not self._is_chinese(match) and len(match) > 20:
                    return match.strip()

            # 查找Output:后的内容
            output_match = re.search(r'Output:\s*(.+?)(?:\n|$)', response, re.IGNORECASE)
            if output_match:
                output_text = output_match.group(1).strip()
                if not self._is_chinese(output_text):
                    return output_text

            # 如果都没找到，返回提示信息
            return "⚠️ LLM returned Chinese text with explanations. Please try again or use English input."

        # 移除常见的标题和解释性文本
        import re

        # 移除常见的标题格式
        response = re.sub(r'\*\*[^*]+\*\*:?\s*', '', response)  # 移除 **Optimized Prompt:** 等
        response = re.sub(r'Optimized Prompt:?\s*', '', response, flags=re.IGNORECASE)
        response = re.sub(r'Output:?\s*', '', response, flags=re.IGNORECASE)
        response = re.sub(r'Result:?\s*', '', response, flags=re.IGNORECASE)

        lines = response.split('\n')
        clean_lines = []

        for line in lines:
            line = line.strip()
            # 跳过解释性行
            if (line.startswith('优化说明') or
                line.startswith('说明') or
                line.startswith('解释') or
                line.startswith('1.') or
                line.startswith('2.') or
                line.startswith('3.') or
                line.startswith('4.') or
                line.startswith('5.') or
                line.startswith('6.') or
                line.startswith('7.') or
                line.startswith('8.') or
                line.startswith('Explanation') or
                line.startswith('Note:') or
                line.startswith('Here') or
                line.startswith('This') or
                line == '' or
                len(line) < 10):  # 跳过太短的行
                continue
            clean_lines.append(line)

        if clean_lines:
            # 取第一行作为主要提示词，并清理引号
            result = clean_lines[0].strip('"').strip("'").strip()
            return result

        # 如果没有找到有效内容，尝试直接清理原始响应
        cleaned = response.strip().strip('"').strip("'").strip()
        if len(cleaned) > 10:
            return cleaned

        return "⚠️ Unable to extract valid prompt from LLM response."

    def _prompt_for_english(self, chinese_prompt: str) -> str:
        """提示用户使用英文提示词"""
        return f"⚠️ LLM翻译服务当前不可用，请直接提供英文提示词。原始输入：{chinese_prompt}"

    def _basic_translate_and_optimize(self, chinese_prompt: str) -> str:
        """基础翻译和优化（当LLM不可用时的回退方案）- Kontext专用"""
        try:
            # Kontext专用的关键词映射翻译（图像编辑相关）
            translation_map = {
                # 编辑动作相关
                '编辑': 'edit', '修改': 'modify', '调整': 'adjust', '优化': 'optimize',
                '变换': 'transform', '转换': 'convert', '改变': 'change',
                '增强': 'enhance', '美化': 'beautify', '润色': 'polish',

                # 风格相关
                '风格': 'style', '艺术': 'artistic', '现代': 'modern', '古典': 'classical',
                '抽象': 'abstract', '写实': 'realistic', '卡通': 'cartoon',
                '油画': 'oil painting', '水彩': 'watercolor', '素描': 'sketch',
                '赛博朋克': 'cyberpunk', '蒸汽朋克': 'steampunk',

                # 颜色和光照
                '颜色': 'color', '色彩': 'color', '明亮': 'bright', '暗': 'dark',
                '鲜艳': 'vibrant', '柔和': 'soft', '对比': 'contrast',
                '饱和': 'saturated', '淡': 'light', '深': 'deep',

                # 时间和光线
                '清晨': 'early morning', '早晨': 'morning', '上午': 'morning',
                '中午': 'noon', '正午': 'midday', '下午': 'afternoon',
                '傍晚': 'evening', '黄昏': 'sunset', '夜晚': 'night', '深夜': 'late night',
                '日出': 'sunrise', '日落': 'sunset', '黎明': 'dawn', '黄昏时分': 'twilight',
                '金色时光': 'golden hour', '蓝调时光': 'blue hour',

                # 图像质量
                '清晰': 'sharp', '模糊': 'blur', '细节': 'detail', '质量': 'quality',
                '高清': 'high resolution', '精细': 'fine', '粗糙': 'rough',

                # 构图和视角
                '构图': 'composition', '视角': 'perspective', '角度': 'angle',
                '特写': 'close-up', '全景': 'panoramic', '俯视': 'top view',

                # 背景和环境
                '背景': 'background', '前景': 'foreground', '环境': 'environment',
                '场景': 'scene', '氛围': 'atmosphere', '情绪': 'mood',

                # 人物和对象
                '人物': 'character', '脸部': 'face', '表情': 'expression',
                '姿势': 'pose', '动作': 'action', '服装': 'clothing',

                # 特效和处理
                '特效': 'effects', '滤镜': 'filter', '光效': 'lighting effects',
                '阴影': 'shadow', '高光': 'highlight', '反射': 'reflection'
            }

            # 进行基础翻译
            translated = chinese_prompt
            for chinese, english in translation_map.items():
                translated = translated.replace(chinese, english)

            # 添加Kontext专用的修饰词
            if any(word in chinese_prompt for word in ['编辑', '修改', '调整']):
                translated += ", precise editing, maintain original structure"

            if any(word in chinese_prompt for word in ['风格', '艺术']):
                translated += ", artistic style transfer, creative enhancement"

            if any(word in chinese_prompt for word in ['颜色', '色彩']):
                translated += ", color correction, vibrant colors"

            # 添加基础的图像编辑质量描述
            translated += ", high quality image editing, professional result"

            self.logger.info(f"Kontext基础翻译优化: {chinese_prompt} -> {translated}")
            return translated.strip()

        except Exception as e:
            self.logger.error(f"基础翻译优化失败: {e}")
            # 最后的回退：返回原始提示词加上基础修饰
            return f"{chinese_prompt}, high quality editing, professional result"

    def apply_lora(self, prompt: str, lora_name: str) -> str:
        """
        根据lora_name将LoRA模型指令插入到prompt中
        """
        lora_tag = self.lora_dict.get(lora_name, f'<lora:{lora_name}>')
        return f"{lora_tag} {prompt}"

    def analyze_prompt(self, prompt: str) -> Dict[str, str]:
        """
        分析prompt，提取关键信息（如风格、主题等）
        """
        analysis = {
            "length": str(len(prompt)),
            "language": "chinese" if self._is_chinese(prompt) else "english",
            "has_quality_keywords": str(any(kw in prompt.lower() for kw in ['high quality', 'detailed', 'masterpiece'])),
            "word_count": str(len(prompt.split()))
        }
        return analysis

kontext_prompt_optimizer = KontextPromptOptimizer()