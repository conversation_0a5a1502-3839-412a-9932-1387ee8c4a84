"""
Flux 标准节点映射器
负责将 Flux 参数映射到标准 ComfyUI 节点 (节点6, 55, 50等)
这是 Flux 工作流的专有特性
"""

import json
import os
from typing import Dict, Any, Optional, List, Tuple
import logging

from .flux_workflow_models import FluxParameters, StandardNodeConfig, ExecutionResult


class StandardNodeMapper:
    """标准 ComfyUI 节点映射器"""
    
    def __init__(self, workflow_path: str = "workflows"):
        self.workflow_path = workflow_path
        self.logger = logging.getLogger(__name__)
        
        # 标准工作流文件
        self.default_workflow_file = "flux_default.json"
        self.fallback_workflow_file = "flux_default.json"  # 使用存在的文件作为备用
        
        # 工作流类型
        self.workflow_type = None  # "flux" 或 "standard"
        
        # 节点映射缓存
        self.node_mappings = {}  # 存储节点ID到功能的映射
    
    def _detect_workflow_type(self, workflow_data: Dict[str, Any]) -> str:
        """
        检测工作流类型
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            str: "flux" 或 "standard"
        """
        try:
            # 检查是否有FluxSamplerParams+节点
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and node_data.get("class_type") == "FluxSamplerParams+":
                    self.logger.info("检测到Flux专用工作流")
                    return "flux"
            
            # 检查是否有KSampler节点
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict) and node_data.get("class_type") == "KSampler":
                    self.logger.info("检测到标准KSampler工作流")
                    return "standard"
            
            # 默认返回flux
            self.logger.warning("无法确定工作流类型，默认使用flux")
            return "flux"
            
        except Exception as e:
            self.logger.error(f"检测工作流类型失败: {e}")
            return "flux"
    
    def _build_node_mappings(self, workflow_data: Dict[str, Any], workflow_type: str) -> Dict[str, str]:
        """
        构建节点ID到功能的映射
        
        Args:
            workflow_data: 工作流数据
            workflow_type: 工作流类型
            
        Returns:
            Dict[str, str]: 节点ID到功能的映射
        """
        mappings = {}
        
        try:
            for node_id, node_data in workflow_data.items():
                if not isinstance(node_data, dict):
                    continue
                
                class_type = node_data.get("class_type", "")
                meta_title = node_data.get("_meta", {}).get("title", "").lower()
                
                # 根据节点类型和元数据识别功能
                if class_type == "CLIPTextEncode":
                    if "positive" in meta_title or "prompt" in meta_title:
                        mappings["positive_text_encode"] = node_id
                    elif "negative" in meta_title:
                        mappings["negative_text_encode"] = node_id
                    else:
                        # 如果没有明确的元数据，检查输入连接
                        inputs = node_data.get("inputs", {})
                        if "positive" in str(inputs):
                            mappings["positive_text_encode"] = node_id
                        elif "negative" in str(inputs):
                            mappings["negative_text_encode"] = node_id
                        else:
                            # 默认作为正面文本编码器
                            mappings["positive_text_encode"] = node_id
                
                elif class_type == "FluxSamplerParams+":
                    mappings["flux_sampler"] = node_id
                
                elif class_type == "KSampler":
                    mappings["ksampler"] = node_id
                
                elif class_type == "EmptyLatentImage":
                    mappings["empty_latent"] = node_id
                
                elif class_type == "VAEDecode":
                    mappings["vae_decode"] = node_id
                
                elif class_type == "SaveImage":
                    mappings["save_image"] = node_id
                
                elif class_type == "DualCLIPLoader":
                    mappings["dual_clip_loader"] = node_id
                
                elif class_type == "UNETLoader":
                    mappings["unet_loader"] = node_id
                
                elif class_type == "VAELoader":
                    mappings["vae_loader"] = node_id
                
                elif class_type == "CheckpointLoaderSimple":
                    mappings["checkpoint_loader"] = node_id
            
            self.logger.info(f"构建节点映射完成: {mappings}")
            return mappings
            
        except Exception as e:
            self.logger.error(f"构建节点映射失败: {e}")
            return {}
    
    def _get_node_config_for_function(self, function_name: str, workflow_type: str) -> Optional[StandardNodeConfig]:
        """
        根据功能名称获取节点配置
        
        Args:
            function_name: 功能名称
            workflow_type: 工作流类型
            
        Returns:
            Optional[StandardNodeConfig]: 节点配置
        """
        if workflow_type == "flux":
            return self._get_flux_node_config(function_name)
        else:
            return self._get_standard_node_config(function_name)
    
    def _get_flux_node_config(self, function_name: str) -> Optional[StandardNodeConfig]:
        """获取Flux节点配置"""
        configs = {
            "positive_text_encode": StandardNodeConfig(
                node_id="",  # 动态设置
                class_type="CLIPTextEncode",
                parameter_mappings={"prompt": "text"},
                default_values={"text": ""},
                input_connections={"clip": ("dual_clip_loader", 0)}
            ),
            "flux_sampler": StandardNodeConfig(
                node_id="",
                class_type="FluxSamplerParams+",
                parameter_mappings={
                    "seed": "seed",
                    "steps": "steps", 
                    "guidance": "guidance"
                },
                default_values={
                    "seed": "?",
                    "steps": "20",
                    "guidance": "3.5",
                    "sampler": "euler",
                    "scheduler": "simple",
                    "denoise": "1",
                    "max_shift": "",
                    "base_shift": ""
                },
                input_connections={
                    "model": ("unet_loader", 0),
                    "conditioning": ("positive_text_encode", 0),
                    "latent_image": ("empty_latent", 0)
                }
            ),
            "empty_latent": StandardNodeConfig(
                node_id="",
                class_type="EmptyLatentImage",
                parameter_mappings={
                    "width": "width",
                    "height": "height"
                },
                default_values={
                    "width": 1024,
                    "height": 1024,
                    "batch_size": 1
                }
            ),
            "vae_decode": StandardNodeConfig(
                node_id="",
                class_type="VAEDecode",
                parameter_mappings={},
                default_values={},
                input_connections={
                    "samples": ("flux_sampler", 0),
                    "vae": ("vae_loader", 0)
                }
            ),
            "save_image": StandardNodeConfig(
                node_id="",
                class_type="SaveImage",
                parameter_mappings={},
                default_values={
                    "filename_prefix": "ComfyUI",
                    "filename_suffix": ""
                },
                input_connections={
                    "images": ("vae_decode", 0)
                }
            )
        }
        return configs.get(function_name)
    
    def _get_standard_node_config(self, function_name: str) -> Optional[StandardNodeConfig]:
        """获取标准节点配置"""
        configs = {
            "positive_text_encode": StandardNodeConfig(
                node_id="",
                class_type="CLIPTextEncode",
                parameter_mappings={"prompt": "text"},
                default_values={"text": ""},
                input_connections={"clip": ("checkpoint_loader", 1)}
            ),
            "negative_text_encode": StandardNodeConfig(
                node_id="",
                class_type="CLIPTextEncode",
                parameter_mappings={"negative_prompt": "text"},
                default_values={"text": ""},
                input_connections={"clip": ("checkpoint_loader", 1)}
            ),
            "ksampler": StandardNodeConfig(
                node_id="",
                class_type="KSampler",
                parameter_mappings={
                    "seed": "seed",
                    "steps": "steps",
                    "guidance": "cfg"
                },
                default_values={
                    "seed": -1,
                    "steps": 20,
                    "cfg": 3.5,
                    "sampler_name": "euler",
                    "scheduler": "simple",
                    "denoise": 1.0
                },
                input_connections={
                    "model": ("checkpoint_loader", 0),
                    "positive": ("positive_text_encode", 0),
                    "negative": ("negative_text_encode", 0),
                    "latent_image": ("empty_latent", 0)
                }
            ),
            "empty_latent": StandardNodeConfig(
                node_id="",
                class_type="EmptyLatentImage",
                parameter_mappings={
                    "width": "width",
                    "height": "height"
                },
                default_values={
                    "width": 1024,
                    "height": 1024,
                    "batch_size": 1
                }
            ),
            "vae_decode": StandardNodeConfig(
                node_id="",
                class_type="VAEDecode",
                parameter_mappings={},
                default_values={},
                input_connections={
                    "samples": ("ksampler", 0),
                    "vae": ("checkpoint_loader", 2)
                }
            ),
            "save_image": StandardNodeConfig(
                node_id="",
                class_type="SaveImage",
                parameter_mappings={},
                default_values={
                    "filename_prefix": "ComfyUI"
                },
                input_connections={
                    "images": ("vae_decode", 0)
                }
            )
        }
        return configs.get(function_name)
    
    def load_workflow_template(self, workflow_file: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        加载工作流模板
        
        Args:
            workflow_file: 工作流文件名，如果为None则使用默认
            
        Returns:
            Dict[str, Any]: 工作流数据，如果失败返回None
        """
        try:
            # 确定工作流文件
            if workflow_file is None:
                workflow_file = self.default_workflow_file
            
            workflow_path = os.path.join(self.workflow_path, workflow_file)
            self.logger.info(f"尝试加载工作流文件: {workflow_path}")
            self.logger.info(f"工作流路径: {self.workflow_path}")
            self.logger.info(f"工作流文件名: {workflow_file}")
            
            # 尝试加载主工作流
            if os.path.exists(workflow_path):
                with open(workflow_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                self.logger.info(f"加载工作流模板: {workflow_file}")
                return workflow_data
            
            # 尝试加载备用工作流
            fallback_path = os.path.join(self.workflow_path, self.fallback_workflow_file)
            if os.path.exists(fallback_path):
                with open(fallback_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                self.logger.warning(f"主工作流不存在，使用备用工作流: {self.fallback_workflow_file}")
                return workflow_data
            
            # 都不存在，创建基础工作流
            self.logger.warning("没有找到工作流文件，创建基础工作流")
            return self._create_basic_workflow()
            
        except Exception as e:
            self.logger.error(f"加载工作流模板失败: {e}")
            return self._create_basic_workflow()
    
    def apply_parameters_to_workflow(self, workflow_data: Dict[str, Any], params: FluxParameters) -> Dict[str, Any]:
        """
        将 Flux 参数应用到工作流（精确更新关键节点，保持工作流精心设置不变）
        
        Args:
            workflow_data: 原始工作流数据
            params: Flux 参数
            
        Returns:
            Dict[str, Any]: 更新后的工作流数据
        """
        try:
            self.logger.info("开始精确更新工作流关键参数，保持精心设置不变")
            self.logger.info(f"🔍 [DEBUG] 输入参数: prompt='{params.prompt[:30]}...', size={params.width}x{params.height}, steps={params.steps}, guidance={params.guidance}")

            # 创建深拷贝避免修改原始数据
            import copy
            updated_workflow = copy.deepcopy(workflow_data)
            
            # 1. 🔥 智能种子更新：根据工作流类型选择正确的种子更新策略
            seed_updated = False
            
            # 第一优先级：查找并更新EasyGlobalSeed节点（控制图、参考图、混合图工作流）
            easy_global_seed_node = None
            for node_id, node_data in updated_workflow.items():
                if (isinstance(node_data, dict) and 
                    node_data.get("class_type") == "easy globalSeed" and 
                    node_data.get("_meta", {}).get("title") == "EasyGlobalSeed"):
                    easy_global_seed_node = (node_id, node_data)
                    break
            
            if easy_global_seed_node:
                node_id, node_data = easy_global_seed_node
                # 记录更新前的状态
                old_value = node_data.get("inputs", {}).get("value", "未设置")
                old_last_seed = node_data.get("inputs", {}).get("last_seed", "未设置")
                self.logger.info(f"🎲 找到EasyGlobalSeed节点{node_id} - 更新前: value={old_value}, last_seed={old_last_seed}")
                
                if hasattr(params, 'seed') and params.seed > 0:
                    if "inputs" in node_data:
                        node_data["inputs"]["value"] = params.seed
                        if "last_seed" in node_data["inputs"]:
                            node_data["inputs"]["last_seed"] = params.seed
                        seed_updated = True
                        self.logger.info(f"✅ 更新EasyGlobalSeed节点{node_id} - 更新后: value={params.seed}, last_seed={params.seed}")
                else:
                    self.logger.warning(f"❌ EasyGlobalSeed节点{node_id}未更新 - params.seed={getattr(params, 'seed', '无seed属性')} (需要>0)")
            else:
                self.logger.info("🔍 未找到EasyGlobalSeed节点，将使用采样器节点更新seed")
            
            # 第二优先级：如果没有EasyGlobalSeed，根据采样器类型更新种子字段
            if not seed_updated and hasattr(params, 'seed') and params.seed > 0:
                # 定义采样器类型与种子字段的映射
                sampler_seed_mapping = {
                    "FluxSamplerParams+": "seed",        # 纯文生图工作流
                    "FluxSamplerParams": "seed",         # 标准Flux采样器
                    "KSamplerAdvanced //Inspire": "noise_seed",  # 控制图等复杂工作流
                    "KSampler": "seed",                  # 标准KSampler
                    "SamplerCustomAdvanced": "noise_seed"
                }
                
                # 🔥 同时更新步骤和引导参数
                sampler_params_mapping = {
                    "FluxSamplerParams+": {"steps": "steps", "guidance": "guidance"},
                    "FluxSamplerParams": {"steps": "steps", "guidance": "guidance"},
                    "KSamplerAdvanced //Inspire": {"steps": "steps", "guidance": "cfg"},
                    "KSampler": {"steps": "steps", "guidance": "cfg"},
                    "SamplerCustomAdvanced": {"steps": "steps", "guidance": "cfg"}
                }
                
                for node_id, node_data in updated_workflow.items():
                    if isinstance(node_data, dict):
                        class_type = node_data.get('class_type')
                        if class_type and class_type in sampler_seed_mapping:
                            if "inputs" in node_data:
                                expected_seed_field = sampler_seed_mapping[class_type]
                                
                                # 检查期望的种子字段是否存在
                                if expected_seed_field in node_data["inputs"]:
                                    # 根据字段类型设置正确的值
                                    if expected_seed_field == "seed" and isinstance(node_data["inputs"]["seed"], str):
                                        # FluxSamplerParams+ 使用字符串类型的种子
                                        node_data["inputs"][expected_seed_field] = str(params.seed)
                                    else:
                                        # 其他采样器使用数字类型的种子
                                        node_data["inputs"][expected_seed_field] = params.seed
                                    
                                    self.logger.info(f"更新{class_type}节点{node_id}的{expected_seed_field}: {params.seed}")
                                    seed_updated = True
                                    
                                    # 🔥 同时更新步骤和引导参数
                                    if class_type in sampler_params_mapping:
                                        param_mapping = sampler_params_mapping[class_type]
                                        if hasattr(params, 'steps') and params.steps > 0 and "steps" in param_mapping:
                                            steps_field = param_mapping["steps"]
                                            if steps_field in node_data["inputs"]:
                                                node_data["inputs"][steps_field] = params.steps
                                                self.logger.info(f"更新{class_type}节点{node_id}的{steps_field}: {params.steps}")
                                        
                                        if hasattr(params, 'guidance') and params.guidance > 0 and "guidance" in param_mapping:
                                            guidance_field = param_mapping["guidance"]
                                            if guidance_field in node_data["inputs"]:
                                                node_data["inputs"][guidance_field] = params.guidance
                                                self.logger.info(f"更新{class_type}节点{node_id}的{guidance_field}: {params.guidance}")
            
            if not seed_updated:
                self.logger.warning("未找到合适的种子更新节点")
            
            # 2. 动态查找并更新提示词节点（支持多种节点类型）
            prompt_updated = False
            if hasattr(params, 'prompt') and params.prompt:
                # 优先查找标题为prompt_input_01的节点（控制图和Redux工作流）
                for node_id, node_data in updated_workflow.items():
                    if (isinstance(node_data, dict) and
                        node_data.get("_meta", {}).get("title") == "prompt_input_01" and
                        "inputs" in node_data):
                        # 🔥 修复：检查节点的输入字段类型，支持text和prompt两种字段
                        if "prompt" in node_data["inputs"]:
                            node_data["inputs"]["prompt"] = params.prompt
                            self.logger.info(f"✅ 更新prompt_input_01节点{node_id}的prompt字段: {params.prompt[:50]}...")
                        elif "text" in node_data["inputs"]:
                            node_data["inputs"]["text"] = params.prompt
                            self.logger.info(f"✅ 更新prompt_input_01节点{node_id}的text字段: {params.prompt[:50]}...")
                        else:
                            self.logger.warning(f"⚠️ prompt_input_01节点{node_id}没有prompt或text字段")
                            continue
                        prompt_updated = True
                        break

                # 如果没找到prompt_input_01，查找CLIPTextEncode节点（文生图工作流）
                if not prompt_updated:
                    for node_id, node_data in updated_workflow.items():
                        if (isinstance(node_data, dict) and
                            node_data.get("class_type") == "CLIPTextEncode" and
                            "inputs" in node_data and
                            "text" in node_data["inputs"]):
                            node_data["inputs"]["text"] = params.prompt
                            prompt_updated = True
                            self.logger.info(f"✅ 更新CLIPTextEncode节点{node_id}的提示词: {params.prompt[:50]}...")
                            break

                if not prompt_updated:
                    self.logger.error(f"❌ 未找到合适的提示词节点，无法更新提示词: {params.prompt}")
                    # 不抛出异常，让工作流继续执行，但记录错误

            # 3. 🔥 新增：动态查找并更新尺寸参数（width/height）
            size_updated = False
            self.logger.info(f"🔍 [DEBUG] 开始查找尺寸节点，目标尺寸: {params.width}x{params.height}")

            if hasattr(params, 'width') and hasattr(params, 'height') and params.width > 0 and params.height > 0:
                # 查找EmptyLatentImage节点
                latent_nodes_found = 0
                for node_id, node_data in updated_workflow.items():
                    if isinstance(node_data, dict):
                        class_type = node_data.get("class_type", "")
                        if class_type in ["EmptyLatentImage", "EmptySD3LatentImage"]:
                            latent_nodes_found += 1
                            self.logger.info(f"🔍 [DEBUG] 发现潜在尺寸节点: {node_id}, 类型: {class_type}")

                            if "inputs" in node_data:
                                old_width = node_data["inputs"].get("width", "未设置")
                                old_height = node_data["inputs"].get("height", "未设置")
                                self.logger.info(f"📏 找到{class_type}节点{node_id} - 更新前: {old_width}x{old_height}")

                                node_data["inputs"]["width"] = params.width
                                node_data["inputs"]["height"] = params.height
                                size_updated = True

                                # 验证更新是否成功
                                new_width = node_data["inputs"].get("width")
                                new_height = node_data["inputs"].get("height")
                                self.logger.info(f"✅ 更新{class_type}节点{node_id} - 更新后: {new_width}x{new_height}")
                                break
                            else:
                                self.logger.warning(f"⚠️ 节点{node_id}没有inputs字段")

                self.logger.info(f"🔍 [DEBUG] 总共发现{latent_nodes_found}个潜在尺寸节点")
                if not size_updated:
                    self.logger.error(f"❌ 未找到合适的尺寸更新节点，无法设置尺寸: {params.width}x{params.height}")
            else:
                self.logger.warning(f"⚠️ 尺寸参数无效: width={getattr(params, 'width', 'None')}, height={getattr(params, 'height', 'None')}")

            # 4. 图片数据由_apply_images_by_type方法专门处理，这里不处理避免冲突
            # 图片数据处理交给FluxWorkflowManager的_apply_images_by_type方法

            # 5. 动态查找并更新LoRA节点（避免硬编码节点ID）
            lora_updated = False
            if hasattr(params, 'lora_name') and params.lora_name:
                # 查找Power Lora Loader节点
                for node_id, node_data in updated_workflow.items():
                    if (isinstance(node_data, dict) and
                        node_data.get("class_type") == "Power Lora Loader" and
                        "inputs" in node_data):
                        node_data["inputs"]["➕ Add Lora"] = params.lora_name
                        lora_updated = True
                        self.logger.info(f"更新Power Lora Loader节点{node_id}的LoRA: {params.lora_name}")
                        break

                # 如果没有找到Power Lora Loader，查找其他LoRA节点类型
                if not lora_updated:
                    lora_node_types = ["LoraLoader", "LoRALoader", "Lora Loader"]
                    for node_id, node_data in updated_workflow.items():
                        if (isinstance(node_data, dict) and
                            node_data.get("class_type") in lora_node_types and
                            "inputs" in node_data):
                            # 尝试不同的输入字段名
                            lora_input_fields = ["lora_name", "model_name", "name"]
                            for field in lora_input_fields:
                                if field in node_data["inputs"]:
                                    node_data["inputs"][field] = params.lora_name
                                    lora_updated = True
                                    self.logger.info(f"更新{node_data.get('class_type')}节点{node_id}的LoRA: {params.lora_name}")
                                    break
                            if lora_updated:
                                break

                if not lora_updated:
                    self.logger.warning("未找到合适的LoRA更新节点")
            
            self.logger.info("保持工作流的精心采样设置不变，只更新了关键参数")
            # 🔍 [DEBUG] 最终验证：检查关键参数是否正确应用
            self.logger.info("🔍 [DEBUG] 最终验证工作流参数应用情况:")

            # 验证尺寸参数
            for node_id, node_data in updated_workflow.items():
                if isinstance(node_data, dict) and node_data.get("class_type") in ["EmptyLatentImage", "EmptySD3LatentImage"]:
                    if "inputs" in node_data:
                        final_width = node_data["inputs"].get("width")
                        final_height = node_data["inputs"].get("height")
                        self.logger.info(f"🔍 [DEBUG] 最终验证 - 节点{node_id}尺寸: {final_width}x{final_height}")

            # 验证提示词参数
            for node_id, node_data in updated_workflow.items():
                if isinstance(node_data, dict):
                    if (node_data.get("_meta", {}).get("title") == "prompt_input_01" and "inputs" in node_data):
                        final_prompt = node_data["inputs"].get("prompt", "")
                        self.logger.info(f"🔍 [DEBUG] 最终验证 - 节点{node_id}提示词: {final_prompt[:30]}...")
                    elif (node_data.get("class_type") == "CLIPTextEncode" and "inputs" in node_data):
                        final_text = node_data["inputs"].get("text", "")
                        self.logger.info(f"🔍 [DEBUG] 最终验证 - 节点{node_id}文本: {final_text[:30]}...")

            return updated_workflow

        except Exception as e:
            self.logger.error(f"精确更新工作流参数失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return workflow_data
    
    def _apply_node_parameters(self, node_data: Dict[str, Any], node_config: StandardNodeConfig, params: FluxParameters, workflow_type: str) -> Dict[str, Any]:
        """应用参数到单个节点"""
        try:
            updated_node = node_data.copy()
            
            # 确保inputs字段存在
            if "inputs" not in updated_node:
                updated_node["inputs"] = {}
            
            # 应用参数映射
            for param_name, input_name in node_config.parameter_mappings.items():
                if input_name is None:
                    continue
                
                # 获取参数值
                param_value = getattr(params, param_name, None)
                if param_value is not None:
                    # 验证参数值
                    validated_value = node_config.get_input_value(input_name, param_value)
                    updated_node["inputs"][input_name] = validated_value
            
            # 应用默认值
            for input_name, default_value in node_config.default_values.items():
                if input_name not in updated_node["inputs"]:
                    updated_node["inputs"][input_name] = default_value
            
            # 应用输入连接（需要将功能名称转换为实际节点ID）
            for input_name, connection in node_config.input_connections.items():
                if input_name not in updated_node["inputs"]:
                    # 将功能名称转换为实际节点ID
                    actual_connection = self._resolve_connection(connection)
                    if actual_connection:
                        updated_node["inputs"][input_name] = actual_connection
            
            return updated_node
            
        except Exception as e:
            self.logger.error(f"应用节点 {node_config.node_id} 参数失败: {e}")
            return node_data
    
    def _resolve_connection(self, connection: Tuple[str, int]) -> Optional[List]:
        """
        解析连接，将功能名称转换为实际节点ID
        
        Args:
            connection: (功能名称, 输出索引)
            
        Returns:
            Optional[List]: [节点ID, 输出索引] 或 None
        """
        try:
            function_name, output_index = connection
            actual_node_id = self.node_mappings.get(function_name)
            if actual_node_id:
                return [actual_node_id, output_index]
            return None
        except Exception as e:
            self.logger.error(f"解析连接失败: {e}")
            return None
    
    def _validate_workflow_structure(self, workflow_data: Dict[str, Any], workflow_type: str):
        """验证工作流结构"""
        if workflow_type == "flux":
            # Flux工作流验证
            required_functions = ["positive_text_encode", "flux_sampler", "empty_latent", "vae_decode", "save_image"]
            node_type_mapping = {
                "positive_text_encode": "CLIPTextEncode",
                "flux_sampler": "FluxSamplerParams+", 
                "empty_latent": "EmptyLatentImage",
                "vae_decode": "VAEDecode",
                "save_image": "SaveImage"
            }
        else:
            # 标准工作流验证
            required_functions = ["positive_text_encode", "negative_text_encode", "ksampler", "empty_latent", "vae_decode", "save_image"]
            node_type_mapping = {
                "positive_text_encode": "CLIPTextEncode",
                "negative_text_encode": "CLIPTextEncode",
                "ksampler": "KSampler", 
                "empty_latent": "EmptyLatentImage",
                "vae_decode": "VAEDecode",
                "save_image": "SaveImage"
            }
        
        missing_functions = []
        for function_name in required_functions:
            if function_name not in self.node_mappings:
                missing_functions.append(function_name)
        
        if missing_functions:
            self.logger.warning(f"{workflow_type}工作流缺少关键功能: {missing_functions}")
        
        # 检查节点类型
        for function_name, expected_type in node_type_mapping.items():
            if function_name in self.node_mappings:
                node_id = self.node_mappings[function_name]
                if node_id in workflow_data:
                    actual_type = workflow_data[node_id].get("class_type")
                    if actual_type != expected_type:
                        self.logger.warning(f"节点 {node_id} ({function_name}) 类型不匹配: 期望 {expected_type}, 实际 {actual_type}")
    
    def get_node_mappings(self, workflow_data: Dict[str, Any]) -> Dict[str, str]:
        """
        获取节点映射信息
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            Dict[str, str]: 功能名称到节点ID的映射
        """
        return self.node_mappings.copy()
    
    def extract_generation_params(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从工作流中提取生成参数
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            Dict[str, Any]: 提取的参数
        """
        params = {}
        
        try:
            # 检测工作流类型
            workflow_type = self._detect_workflow_type(workflow_data)
            
            # 构建节点映射
            node_mappings = self._build_node_mappings(workflow_data, workflow_type)
            
            # 从文本编码器提取提示词
            positive_node_id = node_mappings.get("positive_text_encode")
            if positive_node_id and positive_node_id in workflow_data:
                prompt = workflow_data[positive_node_id].get("inputs", {}).get("text", "")
                params["prompt"] = prompt
            
            # 根据工作流类型提取采样参数
            sampler_node_id = node_mappings.get("flux_sampler") or node_mappings.get("ksampler")
            if sampler_node_id and sampler_node_id in workflow_data:
                sampler_inputs = workflow_data[sampler_node_id].get("inputs", {})
                
                if workflow_type == "flux":
                    # FluxSamplerParams+参数
                    params.update({
                        "seed": sampler_inputs.get("seed", "?"),
                        "steps": sampler_inputs.get("steps", "20"),
                        "guidance": sampler_inputs.get("guidance", "3.5"),
                        "sampler": sampler_inputs.get("sampler", "euler"),
                        "scheduler": sampler_inputs.get("scheduler", "simple")
                    })
                else:
                    # KSampler参数
                    params.update({
                        "seed": sampler_inputs.get("seed", -1),
                        "steps": sampler_inputs.get("steps", 20),
                        "guidance": sampler_inputs.get("cfg", 3.5),
                        "sampler_name": sampler_inputs.get("sampler_name", "euler"),
                        "scheduler": sampler_inputs.get("scheduler", "simple")
                    })
            
            # 从EmptyLatentImage提取尺寸
            latent_node_id = node_mappings.get("empty_latent")
            if latent_node_id and latent_node_id in workflow_data:
                latent_inputs = workflow_data[latent_node_id].get("inputs", {})
                params.update({
                    "width": latent_inputs.get("width", 1024),
                    "height": latent_inputs.get("height", 1024)
                })
            
            # 提取负面提示词（仅对标准工作流）
            if workflow_type == "standard":
                negative_node_id = node_mappings.get("negative_text_encode")
                if negative_node_id and negative_node_id in workflow_data:
                    negative_prompt = workflow_data[negative_node_id].get("inputs", {}).get("text", "")
                    if negative_prompt:
                        params["negative_prompt"] = negative_prompt
            
        except Exception as e:
            self.logger.error(f"提取工作流参数失败: {e}")
        
        return params
    
    def _create_basic_workflow(self) -> Dict[str, Any]:
        """创建基础工作流结构"""
        # 默认创建Flux工作流
        return self._create_flux_workflow()
    
    def _create_flux_workflow(self) -> Dict[str, Any]:
        """创建Flux基础工作流"""
        return {
            "40": {
                "class_type": "DualCLIPLoader",
                "inputs": {
                    "clip_name1": "clip_l.safetensors",
                    "clip_name2": "t5xxl_fp8_e4m3fn.safetensors",
                    "type": "flux",
                    "device": "default"
                }
            },
            "6": {
                "class_type": "CLIPTextEncode",
                "inputs": {
                    "text": "",
                    "clip": ["40", 0]
                }
            },
            "50": {
                "class_type": "EmptyLatentImage",
                "inputs": {
                    "width": 1024,
                    "height": 1024,
                    "batch_size": 1
                }
            },
            "55": {
                "class_type": "FluxSamplerParams+",
                "inputs": {
                    "seed": "?",
                    "steps": "20",
                    "guidance": "3.5",
                    "sampler": "euler",
                    "scheduler": "simple",
                    "denoise": "1",
                    "max_shift": "",
                    "base_shift": "",
                    "model": ["54", 0],
                    "conditioning": ["6", 0],
                    "latent_image": ["50", 0]
                }
            },
            "54": {
                "class_type": "UNETLoader",
                "inputs": {
                    "unet_name": "flux1_dev.safetensors",
                    "weight_dtype": "default"
                }
            },
            "42": {
                "class_type": "VAELoader",
                "inputs": {
                    "vae_name": "Flux_ae.safetensors"
                }
            },
            "51": {
                "class_type": "VAEDecode",
                "inputs": {
                    "samples": ["55", 0],
                    "vae": ["42", 0]
                }
            },
            "61": {
                "class_type": "SaveImage",
                "inputs": {
                    "images": ["51", 0],
                    "filename_prefix": "ComfyUI",
                    "filename_suffix": ""
                }
            }
        }
    
    def _create_standard_workflow(self) -> Dict[str, Any]:
        """创建标准基础工作流（包含负面提示词）"""
        return {
            "49": {
                "class_type": "CheckpointLoaderSimple",
                "inputs": {
                    "ckpt_name": "flux1-dev.safetensors"
                }
            },
            "6": {
                "class_type": "CLIPTextEncode",
                "inputs": {
                    "text": "",
                    "clip": ["49", 1]
                }
            },
            "7": {
                "class_type": "CLIPTextEncode",
                "inputs": {
                    "text": "",
                    "clip": ["49", 1]
                }
            },
            "50": {
                "class_type": "EmptyLatentImage",
                "inputs": {
                    "width": 1024,
                    "height": 1024,
                    "batch_size": 1
                }
            },
            "55": {
                "class_type": "KSampler",
                "inputs": {
                    "seed": -1,
                    "steps": 20,
                    "cfg": 3.5,
                    "sampler_name": "euler",
                    "scheduler": "simple",
                    "denoise": 1.0,
                    "model": ["49", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["50", 0]
                }
            },
            "61": {
                "class_type": "VAEDecode",
                "inputs": {
                    "samples": ["55", 0],
                    "vae": ["49", 2]
                }
            },
            "60": {
                "class_type": "SaveImage",
                "inputs": {
                    "images": ["61", 0],
                    "filename_prefix": "ComfyUI"
                }
            }
        }
    
    def update_node_config(self, function_name: str, config_updates: Dict[str, Any]):
        """
        更新节点配置
        
        Args:
            function_name: 功能名称
            config_updates: 配置更新
        """
        if function_name in self.node_mappings:
            node_config = self._get_node_config_for_function(function_name, self.workflow_type or "flux")
            if node_config:
                # 更新默认值
                if "default_values" in config_updates:
                    node_config.default_values.update(config_updates["default_values"])
                
                # 更新参数映射
                if "parameter_mappings" in config_updates:
                    node_config.parameter_mappings.update(config_updates["parameter_mappings"])
                
                # 更新值约束
                if "value_constraints" in config_updates:
                    node_config.value_constraints.update(config_updates["value_constraints"])
    
    def get_supported_parameters(self) -> List[str]:
        """
        获取支持的参数列表
        
        Returns:
            List[str]: 支持的参数列表
        """
        supported_params = set()
        
        # 遍历所有功能配置
        for function_name in ["positive_text_encode", "flux_sampler", "ksampler", "empty_latent"]:
            node_config = self._get_node_config_for_function(function_name, self.workflow_type or "flux")
            if node_config:
                supported_params.update(node_config.parameter_mappings.keys())
        
        return list(supported_params)
    
    def get_node_info(self, function_name: str) -> Optional[Dict[str, Any]]:
        """
        获取节点信息
        
        Args:
            function_name: 功能名称
            
        Returns:
            Optional[Dict[str, Any]]: 节点信息
        """
        if function_name in self.node_mappings:
            node_config = self._get_node_config_for_function(function_name, self.workflow_type or "flux")
            if node_config:
                return {
                    "node_id": node_config.node_id,
                    "class_type": node_config.class_type,
                    "parameter_mappings": node_config.parameter_mappings,
                    "default_values": node_config.default_values
                }
        return None
    
    def _apply_negative_prompt(self, workflow_data: Dict[str, Any], negative_prompt: str) -> Dict[str, Any]:
        """
        应用负面提示词到工作流（仅用于标准工作流）
        
        Args:
            workflow_data: 工作流数据
            negative_prompt: 负面提示词
            
        Returns:
            Dict[str, Any]: 更新后的工作流数据
        """
        try:
            # 查找负面文本编码器节点
            negative_node_id = self.node_mappings.get("negative_text_encode")
            if negative_node_id and negative_node_id in workflow_data:
                node_data = workflow_data[negative_node_id]
                # 确保inputs字段存在
                if "inputs" not in node_data:
                    node_data["inputs"] = {}
                
                # 设置负面提示词
                node_data["inputs"]["text"] = negative_prompt
                self.logger.info(f"已设置负面提示词到节点 {negative_node_id}: {negative_prompt}")
            else:
                self.logger.warning("未找到负面文本编码器节点")
            
            return workflow_data
            
        except Exception as e:
            self.logger.error(f"应用负面提示词失败: {e}")
            return workflow_data


# 全局单例
standard_node_mapper: Optional[StandardNodeMapper] = None

def get_standard_node_mapper(workflow_path: str = "workflows") -> StandardNodeMapper:
    """获取标准节点映射器单例"""
    global standard_node_mapper
    if standard_node_mapper is None:
        standard_node_mapper = StandardNodeMapper(workflow_path)
    return standard_node_mapper 