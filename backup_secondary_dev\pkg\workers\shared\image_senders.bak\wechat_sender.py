"""
[二次开发] 微信图片发送器
统一的微信图片发送实现，避免重复代码

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：提供统一的微信图片发送接口
- 维护者：开发团队
- 最后更新：2025-01-13
- 相关任务：代码冗余清理
- 依赖关系：pkg.platform.types.message
"""

import tempfile
import os
from typing import Any, Optional
from pkg.platform.types import message as platform_message


class WeChatImageSender:
    """统一的微信图片发送器"""
    
    def __init__(self, logger=None):
        """
        初始化微信图片发送器
        
        Args:
            logger: 日志记录器，可选
        """
        self.logger = logger
    
    def _log(self, message: str, level: str = "info"):
        """记录日志"""
        if self.logger:
            if level == "error":
                self.logger.error(message)
            elif level == "warning":
                self.logger.warning(message)
            else:
                self.logger.info(message)
    
    async def send_image_to_wechat(self, image_data: bytes, query: Any) -> bool:
        """
        发送图片到微信
        
        Args:
            image_data: 图片二进制数据
            query: 查询对象，包含adapter和message_event
            
        Returns:
            bool: 发送是否成功
        """
        temp_path = None
        try:
            self._log(f"开始发送图片到微信，图片大小: {len(image_data)} bytes")

            # 保存图片到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_path = temp_file.name

            self._log(f"图片已保存到临时文件: {temp_path}")

            # 创建微信图片消息
            image_message = platform_message.Image(path=temp_path)
            message_chain = platform_message.MessageChain([image_message])

            self._log("开始发送图片消息到微信...")

            # 发送消息
            await query.adapter.reply_message(
                message_source=query.message_event,
                message=message_chain,
                quote_origin=False
            )

            self._log("✅ 图片已成功发送到微信")
            return True

        except Exception as e:
            self._log(f"发送图片到微信失败: {e}", "error")
            return False
        finally:
            # 清理临时文件
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    self._log(f"临时文件已删除: {temp_path}")
                except Exception as e:
                    self._log(f"删除临时文件失败: {e}", "warning")
    
    async def create_image_message_base64(self, image_data: bytes) -> Optional[Any]:
        """
        创建base64格式的图片消息（用于某些特殊场景）
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            图片消息对象或None
        """
        try:
            import base64
            from ....provider import entities as llm_entities

            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            image_content = llm_entities.ContentElement.from_image_base64(image_base64)

            return llm_entities.Message(
                role='assistant',
                content=[image_content]
            )

        except Exception as e:
            self._log(f"创建base64图片消息失败: {e}", "error")
            return None
