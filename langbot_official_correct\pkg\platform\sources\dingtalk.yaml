apiVersion: v1
kind: MessagePlatformAdapter
metadata:
  name: dingtalk
  label:
    en_US: DingTalk
    zh_Hans: 钉钉
  description:
    en_US: DingTalk Adapter
    zh_Hans: 钉钉适配器，请查看文档了解使用方式
  icon: dingtalk.svg
spec:
  config:
    - name: client_id
      label:
        en_US: Client ID
        zh_Hans: 客户端ID
      type: string
      required: true
      default: ""
    - name: client_secret
      label:
        en_US: Client Secret
        zh_Hans: 客户端密钥
      type: string
      required: true
      default: ""
    - name: robot_code
      label:
        en_US: Robot Code
        zh_Hans: 机器人代码
      type: string
      required: true
      default: ""
    - name: robot_name
      label:
        en_US: Robot Name
        zh_Hans: 机器人名称
      type: string
      required: true
      default: ""
    - name: markdown_card
      label:
        en_US: Markdown Card
        zh_Hans: 是否使用 Markdown 卡片
      type: boolean
      required: false
      default: true
execution:
  python:
    path: ./dingtalk.py
    attr: DingTalkAdapter
