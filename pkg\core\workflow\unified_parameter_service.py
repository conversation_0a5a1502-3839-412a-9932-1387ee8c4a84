"""
统一参数服务
整合命令行参数解析和LLM参数分析，为不同工作流提供统一的参数处理
"""

import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field

from ..intent.parameter_parser import parameter_parser, ParsedParameters
from .unified_routing_system import UnifiedRoutingSystem, ParameterAnalysisResult, WorkflowSubType
from .workflow_parameter_adapter import AdaptedParameters


@dataclass
class UnifiedParameterResult:
    """统一参数分析结果"""
    # 命令行解析结果
    command_line_params: ParsedParameters
    
    # LLM分析结果
    llm_params: ParameterAnalysisResult
    
    # 最终合并的参数
    final_params: Dict[str, Any] = field(default_factory=dict)
    
    # 处理统计
    processing_time_ms: float = 0.0
    success: bool = False
    error_message: str = ""


class UnifiedParameterService:
    """统一参数服务 - 整合命令行参数解析和LLM参数分析"""
    
    def __init__(self, ap=None):
        self.ap = ap
        self.logger = logging.getLogger(__name__)
        self.unified_router = UnifiedRoutingSystem(ap)
    
    async def analyze_user_input(
        self, 
        user_text: str, 
        query: Any,
        workflow_subtype: Optional[WorkflowSubType] = None
    ) -> UnifiedParameterResult:
        """
        统一分析用户输入参数
        
        Args:
            user_text: 用户输入文本
            query: 查询对象
            workflow_subtype: 工作流子类型（可选）
            
        Returns:
            UnifiedParameterResult: 统一参数分析结果
        """
        start_time = time.time()
        result = UnifiedParameterResult(
            command_line_params=ParsedParameters(clean_prompt=""),
            llm_params=ParameterAnalysisResult(success=False),
            success=False
        )
        
        try:
            # 步骤1: 解析命令行参数
            self.logger.info("步骤1: 解析命令行参数")
            parsed_params = parameter_parser.parse_user_input(user_text)
            result.command_line_params = parsed_params
            
            self.logger.info(f"命令行参数解析完成: {parsed_params.raw_parameters}")
            
            # 步骤2: LLM分析技术参数
            self.logger.info("步骤2: LLM分析技术参数")
            llm_params = await self.unified_router.analyze_parameters(
                parsed_params.clean_prompt, 
                query
            )
            result.llm_params = llm_params
            
            if llm_params.success:
                self.logger.info(f"LLM参数分析成功，置信度: {llm_params.confidence}")
            else:
                self.logger.warning(f"LLM参数分析失败: {llm_params.error_message}")
            
            # 步骤3: 合并参数
            self.logger.info("步骤3: 合并参数")
            final_params = self._merge_parameters(parsed_params, llm_params)
            result.final_params = final_params
            
            # 步骤4: 如果指定了工作流类型，进行参数适配
            if workflow_subtype and llm_params.success:
                self.logger.info(f"步骤4: 适配参数到工作流 {workflow_subtype.value}")
                adapted_params = await self.unified_router.adapt_parameters_for_workflow(
                    workflow_subtype=workflow_subtype,
                    llm_params=llm_params.parameters,
                    input_image_size=None  # 可以后续添加图片尺寸获取逻辑
                )
                
                # 更新最终参数
                result.final_params.update({
                    'prompt': adapted_params.prompt,
                    'negative_prompt': adapted_params.negative_prompt,
                    'width': adapted_params.width,
                    'height': adapted_params.height,
                    'steps': adapted_params.steps,
                    'guidance': adapted_params.guidance,
                    'seed': adapted_params.seed,
                    'adaptation_notes': adapted_params.adaptation_notes
                })
            
            result.success = True
            self.logger.info(f"统一参数分析完成，最终参数: {result.final_params}")
            
        except Exception as e:
            self.logger.error(f"统一参数分析失败: {e}")
            result.error_message = str(e)
            result.success = False
        
        finally:
            result.processing_time_ms = (time.time() - start_time) * 1000
        
        return result
    
    def _merge_parameters(
        self, 
        parsed_params: ParsedParameters, 
        llm_params: ParameterAnalysisResult
    ) -> Dict[str, Any]:
        """
        合并命令行参数和LLM参数
        
        优先级：命令行参数 > LLM参数 > 默认值
        """
        merged = {}
        
        # 基础参数（来自LLM分析）
        if llm_params.success and llm_params.parameters:
            merged.update(llm_params.parameters)
        
        # 命令行参数（优先级最高）
        # 种子参数
        if parsed_params.seed is not None:
            merged['seed'] = parsed_params.seed
            merged['seed_instruction'] = 'specific'
        
        # Civitai参数
        if parsed_params.use_civitai:
            merged['use_civitai'] = True
            # 优先级：用户手动指定 > LLM提取
            if parsed_params.civitai_query:
                merged['civitai_query'] = parsed_params.civitai_query
                self.logger.info(f"使用用户指定的Civitai关键词: {merged['civitai_query']}")
            elif llm_params.success and llm_params.parameters.get('civitai_search_tags'):
                tags = llm_params.parameters.get('civitai_search_tags')
                if tags: # 确保标签列表不为空
                    merged['civitai_query'] = " ".join(tags)
                    self.logger.info(f"使用LLM提取的Civitai关键词: {merged['civitai_query']}")
                else:
                    merged['civitai_query'] = "" # LLM返回空列表，不搜索
                    self.logger.info("LLM提取的Civitai关键词为空，不执行搜索")
            else:
                merged['civitai_query'] = "" # 如果没有关键词，则不搜索
                self.logger.info("未找到用户指定或LLM提取的Civitai关键词，不执行搜索")
        
        # LoRA参数
        if parsed_params.lora_names:
            merged['lora_names'] = parsed_params.lora_names
            merged['lora_weights'] = parsed_params.lora_weights
        
        # 确保必要参数存在
        merged.setdefault('prompt', parsed_params.clean_prompt)
        merged.setdefault('width', 1024)
        merged.setdefault('height', 1024)
        merged.setdefault('steps', 20)
        merged.setdefault('guidance', 3.5)
        merged.setdefault('seed_instruction', 'random')
        
        # 添加元数据
        merged['command_line_params'] = parsed_params.raw_parameters
        merged['clean_prompt'] = parsed_params.clean_prompt
        merged['llm_confidence'] = llm_params.confidence if llm_params.success else 0.0
        
        return merged
    
    def get_parameter_summary(self, result: UnifiedParameterResult) -> str:
        """获取参数分析摘要"""
        if not result.success:
            return f"❌ 参数分析失败: {result.error_message}"
        
        summary = f"✅ 参数分析完成 ({result.processing_time_ms:.1f}ms)\n\n"
        
        # 命令行参数
        if result.command_line_params.raw_parameters:
            summary += f"🔧 **命令行参数**: {result.command_line_params.raw_parameters}\n"
        
        # LLM分析
        if result.llm_params.success:
            summary += f"🤖 **LLM分析**: 置信度 {result.llm_params.confidence:.2f}\n"
        else:
            summary += f"⚠️ **LLM分析**: 失败 ({result.llm_params.error_message})\n"
        
        # 最终参数
        final = result.final_params
        summary += f"📋 **最终参数**:\n"
        summary += f"  - 提示词: {final.get('prompt', 'N/A')[:50]}...\n"
        summary += f"  - 尺寸: {final.get('width', 'N/A')}x{final.get('height', 'N/A')}\n"
        summary += f"  - 步数: {final.get('steps', 'N/A')}\n"
        summary += f"  - 引导: {final.get('guidance', 'N/A')}\n"
        
        if final.get('seed'):
            summary += f"  - 种子: {final.get('seed')}\n"
        
        if final.get('use_civitai'):
            summary += f"  - Civitai: {final.get('civitai_query', 'N/A')}\n"
        
        return summary


def get_unified_parameter_service(ap=None) -> UnifiedParameterService:
    """获取统一参数服务实例"""
    return UnifiedParameterService(ap) 