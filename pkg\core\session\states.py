"""
会话状态工具模块
提供命令识别、状态转换等实用函数
"""

import re
from typing import Set, List
from .models import SessionState


# 执行指令关键词
EXECUTION_KEYWORDS: Set[str] = {
    "go", "开始", "生成", "执行", "run", "start", "generate", "exec",
    "绘制", "画", "制作", "create", "make", "draw", "好的", "确定", "ok"
}

# 取消指令关键词
CANCEL_KEYWORDS: Set[str] = {
    "取消", "停止", "退出", "结束", "cancel", "stop", "exit", "quit", "end",
    "算了", "不要", "放弃", "abandon", "abort"
}

# 触发词关键词
TRIGGER_KEYWORDS: Set[str] = {
    "aigen", "kontext", "kontext_api"
}


def is_execution_command(text: str) -> bool:
    """
    判断是否为执行指令（go/开始）
    """
    if not text:
        return False
    
    text_lower = text.strip().lower()
    
    # 精确匹配
    if text_lower in EXECUTION_KEYWORDS:
        return True
    
    # 模糊匹配
    execution_patterns = [
        r'^(go|开始|生成|执行|start|run).*',
        r'.*(开始生成|开始执行|开始绘制).*',
        r'^(好的?|确定|ok|可以)$'
    ]
    
    for pattern in execution_patterns:
        if re.match(pattern, text_lower):
            return True
    
    return False


def is_cancel_command(text: str) -> bool:
    """
    判断是否为取消指令
    """
    if not text:
        return False
    
    text_lower = text.strip().lower()
    
    # 精确匹配
    if text_lower in CANCEL_KEYWORDS:
        return True
    
    # 模糊匹配
    cancel_patterns = [
        r'^(取消|停止|退出|结束).*',
        r'.*(算了|不要了|放弃).*',
        r'^(cancel|stop|quit|exit)$'
    ]
    
    for pattern in cancel_patterns:
        if re.match(pattern, text_lower):
            return True
    
    return False


def is_trigger_word(text: str) -> bool:
    """
    判断是否包含触发词
    支持有空格和无空格两种格式：kontext 提示词 或 kontext提示词
    """
    if not text:
        return False

    text_lower = text.strip().lower()

    # 检查触发词（支持有空格和无空格）
    for keyword in TRIGGER_KEYWORDS:
        keyword_lower = keyword.lower()
        # 支持两种格式：有空格和无空格
        if text_lower.startswith(keyword_lower + " "):
            return True
        elif text_lower == keyword_lower:
            return True
        elif (text_lower.startswith(keyword_lower) and
              len(text_lower) > len(keyword_lower) and
              (not text_lower[len(keyword_lower)].isascii() or
               not text_lower[len(keyword_lower)].isalpha())):
            return True

    return False


def get_next_state_for_workflow(workflow_type_str: str, has_prompt: bool, has_images: bool) -> SessionState:
    """
    根据工作流类型和当前状态获取下一个状态
    
    新的状态机设计：
    - 触发词驱动：只有检测到触发词才进入收集模式
    - 累积收集：收集阶段持续累积文本和图片，不做工作流判断
    - 一次性决策：go指令触发时，基于完整上下文做最终工作流选择
    """
    from .models import WorkflowType
    
    try:
        workflow_type = WorkflowType(workflow_type_str)
    except ValueError:
        workflow_type = WorkflowType.UNKNOWN
    
    # 所有工作流类型都从收集模式开始
    return SessionState.COLLECTING


def can_transition_to(current_state: SessionState, target_state: SessionState) -> bool:
    """
    检查是否可以从当前状态转换到目标状态
    
    新的状态转换规则：
    - 等待触发词 → 收集模式（检测到触发词）
    - 收集模式 → 收集模式（继续收集）
    - 收集模式 → 执行模式（收到go指令）
    - 执行模式 → 等待触发词（生成完成）
    - 执行模式 → 收集模式（用户继续添加内容）
    """
    # 定义状态转换规则
    valid_transitions = {
        SessionState.IDLE: [SessionState.COLLECTING, SessionState.WAITING_FOR_PROMPT],
        SessionState.COLLECTING: [SessionState.PROCESSING, SessionState.GENERATING, 
                                 SessionState.CANCELLED, SessionState.TIMEOUT],
        SessionState.WAITING_FOR_PROMPT: [SessionState.COLLECTING, SessionState.CANCELLED],
        SessionState.WAITING_FOR_IMAGES: [SessionState.COLLECTING, SessionState.CANCELLED],
        SessionState.WAITING_FOR_IMAGE_TYPES: [SessionState.COLLECTING, SessionState.CANCELLED],
        SessionState.READY: [SessionState.PROCESSING, SessionState.CANCELLED],
        SessionState.READY_FOR_GENERATION: [SessionState.GENERATING, SessionState.CANCELLED],
        SessionState.PROCESSING: [SessionState.COMPLETED, SessionState.CANCELLED, SessionState.COLLECTING],
        SessionState.GENERATING: [SessionState.COMPLETED, SessionState.CANCELLED, SessionState.COLLECTING],
        SessionState.COMPLETED: [SessionState.IDLE, SessionState.COLLECTING],
        SessionState.CANCELLED: [SessionState.IDLE],
        SessionState.TIMEOUT: [SessionState.IDLE]
    }
    
    # 任何状态都可以转换到TIMEOUT或CANCELLED
    if target_state in [SessionState.TIMEOUT, SessionState.CANCELLED]:
        return True
    
    allowed_states = valid_transitions.get(current_state, [])
    return target_state in allowed_states


def get_state_description(state: SessionState) -> str:
    """
    获取状态的中文描述
    """
    descriptions = {
        SessionState.IDLE: "空闲",
        SessionState.COLLECTING: "收集参数中",
        SessionState.WAITING_FOR_PROMPT: "等待提示词",
        SessionState.WAITING_FOR_IMAGES: "等待图片",
        SessionState.WAITING_FOR_IMAGE_TYPES: "等待图片类型确认",
        SessionState.READY: "准备就绪",
        SessionState.READY_FOR_GENERATION: "准备生成",
        SessionState.PROCESSING: "处理中",
        SessionState.GENERATING: "生成中",
        SessionState.COMPLETED: "已完成",
        SessionState.CANCELLED: "已取消",
        SessionState.TIMEOUT: "已超时"
    }
    return descriptions.get(state, "未知状态")


def is_collecting_state(state: SessionState) -> bool:
    """
    判断是否是收集状态
    """
    return state in [
        SessionState.COLLECTING,
        SessionState.WAITING_FOR_PROMPT,
        SessionState.WAITING_FOR_IMAGES,
        SessionState.WAITING_FOR_IMAGE_TYPES
    ]


def is_execution_state(state: SessionState) -> bool:
    """
    判断是否是执行状态
    """
    return state in [
        SessionState.PROCESSING,
        SessionState.GENERATING
    ]


def is_final_state(state: SessionState) -> bool:
    """
    判断是否是最终状态
    """
    return state in [
        SessionState.COMPLETED,
        SessionState.CANCELLED,
        SessionState.TIMEOUT
    ] 