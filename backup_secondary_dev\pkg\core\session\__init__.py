"""
核心会话管理模块
提供统一的会话管理接口，支持所有工作流类型
"""

from .manager import SessionManager, session_manager
from .models import (
    WorkflowType, 
    SessionState, 
    WorkflowSession,
    SessionImage,
    FluxImageType,
    session_state_from_string
)
from .states import is_execution_command, is_cancel_command

__all__ = [
    'SessionManager',
    'session_manager', 
    'WorkflowType',
    'SessionState',
    'WorkflowSession',
    'SessionImage',
    'FluxImageType',
    'session_state_from_string',
    'is_execution_command',
    'is_cancel_command'
] 