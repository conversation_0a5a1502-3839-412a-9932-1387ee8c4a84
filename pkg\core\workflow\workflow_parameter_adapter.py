"""
工作流参数适配器
负责将LLM的完整参数建议适配到不同工作流的具体需求
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
import logging


class WorkflowType(Enum):
    """工作流类型"""
    AIGEN_TEXT_ONLY = "aigen_text_only"           # 纯文生图
    AIGEN_CONTROL_ONLY = "aigen_control_only"     # 控制图
    AIGEN_REFERENCE_ONLY = "aigen_reference_only" # 参考图
    AIGEN_CONTROL_REFERENCE = "aigen_control_reference" # 控制+参考
    KONTEXT_1IMAGE = "kontext_1image"             # 1张图片处理
    KONTEXT_2IMAGE = "kontext_2image"             # 2张图片处理
    KONTEXT_3IMAGE = "kontext_3image"             # 3张图片处理
    KONTEXT_API_1IMAGE = "kontext_api_1image"     # 1张图片API
    KONTEXT_API_2IMAGE = "kontext_api_2image"     # 2张图片API
    KONTEXT_API_3IMAGE = "kontext_api_3image"     # 3张图片API


@dataclass
class WorkflowParameterRequirements:
    """工作流参数需求定义"""
    workflow_type: WorkflowType
    needs_width_height: bool = True      # 是否需要具体宽高
    needs_aspect_ratio: bool = False     # 是否需要宽高比
    needs_steps: bool = True             # 是否需要步数
    needs_guidance: bool = True          # 是否需要引导
    needs_seed: bool = True              # 是否需要种子
    follows_input_image_size: bool = False  # 是否跟随输入图片尺寸
    default_aspect_ratio: str = "1:1"   # 默认宽高比
    default_steps: int = 20              # 默认步数
    default_guidance: float = 3.5        # 默认引导


@dataclass
class AdaptedParameters:
    """适配后的参数"""
    workflow_type: WorkflowType
    prompt: str = ""
    negative_prompt: str = ""
    width: Optional[int] = None
    height: Optional[int] = None
    aspect_ratio: Optional[str] = None
    steps: Optional[int] = None
    guidance: Optional[float] = None
    seed: Optional[int] = None
    prompt_upsampling: bool = False
    
    # 元数据
    original_llm_params: Dict[str, Any] = field(default_factory=dict)
    adaptation_notes: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "prompt": self.prompt,
            "negative_prompt": self.negative_prompt,
            "prompt_upsampling": self.prompt_upsampling
        }
        
        # 根据工作流类型添加相应参数
        if self.width is not None and self.height is not None:
            result["width"] = self.width
            result["height"] = self.height
        
        if self.aspect_ratio is not None:
            result["aspect_ratio"] = self.aspect_ratio
        
        if self.steps is not None:
            result["steps"] = self.steps
        
        if self.guidance is not None:
            result["guidance"] = self.guidance
        
        if self.seed is not None:
            result["seed"] = self.seed
        
        return result


class WorkflowParameterAdapter:
    """工作流参数适配器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 定义各工作流的参数需求
        self.workflow_requirements = {
            # AIGEN 工作流 - 需要具体宽高
            WorkflowType.AIGEN_TEXT_ONLY: WorkflowParameterRequirements(
                workflow_type=WorkflowType.AIGEN_TEXT_ONLY,
                needs_width_height=True,
                needs_aspect_ratio=False,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False
            ),
            WorkflowType.AIGEN_CONTROL_ONLY: WorkflowParameterRequirements(
                workflow_type=WorkflowType.AIGEN_CONTROL_ONLY,
                needs_width_height=True,
                needs_aspect_ratio=False,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=True  # 控制图跟随输入图片尺寸
            ),
            WorkflowType.AIGEN_REFERENCE_ONLY: WorkflowParameterRequirements(
                workflow_type=WorkflowType.AIGEN_REFERENCE_ONLY,
                needs_width_height=True,
                needs_aspect_ratio=False,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=True  # 参考图跟随输入图片尺寸
            ),
            WorkflowType.AIGEN_CONTROL_REFERENCE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.AIGEN_CONTROL_REFERENCE,
                needs_width_height=True,
                needs_aspect_ratio=False,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=True  # 控制+参考图跟随输入图片尺寸
            ),
            
            # KONTEXT 工作流 - 只需要宽高比
            WorkflowType.KONTEXT_1IMAGE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.KONTEXT_1IMAGE,
                needs_width_height=False,
                needs_aspect_ratio=True,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False,
                default_aspect_ratio="3:2"
            ),
            WorkflowType.KONTEXT_2IMAGE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.KONTEXT_2IMAGE,
                needs_width_height=False,
                needs_aspect_ratio=True,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False,
                default_aspect_ratio="1:1"
            ),
            WorkflowType.KONTEXT_3IMAGE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.KONTEXT_3IMAGE,
                needs_width_height=False,
                needs_aspect_ratio=True,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False,
                default_aspect_ratio="1:1"
            ),
            
            # KONTEXT_API 工作流 - 只需要宽高比
            WorkflowType.KONTEXT_API_1IMAGE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.KONTEXT_API_1IMAGE,
                needs_width_height=False,
                needs_aspect_ratio=True,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False,
                default_aspect_ratio="3:2"
            ),
            WorkflowType.KONTEXT_API_2IMAGE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.KONTEXT_API_2IMAGE,
                needs_width_height=False,
                needs_aspect_ratio=True,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False,
                default_aspect_ratio="1:1"
            ),
            WorkflowType.KONTEXT_API_3IMAGE: WorkflowParameterRequirements(
                workflow_type=WorkflowType.KONTEXT_API_3IMAGE,
                needs_width_height=False,
                needs_aspect_ratio=True,
                needs_steps=True,
                needs_guidance=True,
                needs_seed=True,
                follows_input_image_size=False,
                default_aspect_ratio="1:1"
            )
        }
    
    def adapt_parameters(
        self,
        workflow_type: WorkflowType,
        llm_params: Dict[str, Any],
        input_image_size: Optional[Tuple[int, int]] = None
    ) -> AdaptedParameters:
        """
        将LLM的完整参数建议适配到特定工作流
        
        Args:
            workflow_type: 工作流类型
            llm_params: LLM分析的完整参数
            input_image_size: 输入图片尺寸 (width, height)，用于跟随输入图片尺寸的工作流
            
        Returns:
            AdaptedParameters: 适配后的参数
        """
        requirements = self.workflow_requirements.get(workflow_type)
        if not requirements:
            self.logger.warning(f"未知的工作流类型: {workflow_type}")
            return self._create_default_adapted_params(workflow_type, llm_params)
        
        adapted = AdaptedParameters(
            workflow_type=workflow_type,
            original_llm_params=llm_params.copy()
        )
        
        # 1. 提取提示词（所有工作流都需要）
        adapted.prompt = llm_params.get('prompt', '')
        adapted.negative_prompt = llm_params.get('negative_prompt', '')
        
        # 2. 处理尺寸参数
        if requirements.follows_input_image_size and input_image_size:
            # 跟随输入图片尺寸
            adapted.width, adapted.height = input_image_size
            adapted.adaptation_notes.append(f"跟随输入图片尺寸: {input_image_size[0]}x{input_image_size[1]}")
        elif requirements.needs_width_height:
            # 需要具体宽高
            adapted.width = llm_params.get('width', 1024)
            adapted.height = llm_params.get('height', 1024)
            adapted.adaptation_notes.append(f"使用LLM建议尺寸: {adapted.width}x{adapted.height}")
        elif requirements.needs_aspect_ratio:
            # 只需要宽高比
            aspect_ratio = llm_params.get('aspect_ratio', requirements.default_aspect_ratio)
            adapted.aspect_ratio = self._normalize_aspect_ratio(aspect_ratio)
            adapted.adaptation_notes.append(f"使用宽高比: {adapted.aspect_ratio}")
        
        # 3. 处理生成参数
        if requirements.needs_steps:
            adapted.steps = llm_params.get('steps', requirements.default_steps)
        
        if requirements.needs_guidance:
            adapted.guidance = llm_params.get('guidance', requirements.default_guidance)
        
        if requirements.needs_seed:
            seed_instruction = llm_params.get('seed_instruction', 'random')
            if seed_instruction == 'random':
                adapted.seed = -1  # 随机种子
                adapted.adaptation_notes.append("使用随机种子")
            elif seed_instruction == 'use_last':
                adapted.seed = -1  # 使用上次种子（由工作流管理器处理）
                adapted.adaptation_notes.append("使用上次种子")
            elif seed_instruction.startswith('specific:'):
                try:
                    adapted.seed = int(seed_instruction.split(':')[1])
                    adapted.adaptation_notes.append(f"使用指定种子: {adapted.seed}")
                except:
                    adapted.seed = -1
                    adapted.adaptation_notes.append("种子格式错误，使用随机种子")
            else:
                adapted.seed = -1
                adapted.adaptation_notes.append("使用随机种子")
        
        # 4. 处理Kontext特有参数
        if workflow_type.value.startswith('kontext'):
            adapted.prompt_upsampling = llm_params.get('prompt_upsampling', False)
        
        self.logger.info(f"参数适配完成 - {workflow_type.value}: {adapted.adaptation_notes}")
        return adapted
    
    def _normalize_aspect_ratio(self, aspect_ratio: str) -> str:
        """标准化宽高比格式"""
        # 支持多种格式
        ratio_mapping = {
            "square": "1:1",
            "portrait": "2:3", 
            "landscape": "3:2",
            "wide": "16:9",
            "tall": "9:16"
        }
        
        # 如果是英文描述，转换为数字比例
        if aspect_ratio in ratio_mapping:
            return ratio_mapping[aspect_ratio]
        
        # 如果是数字比例，验证格式
        if ':' in aspect_ratio:
            try:
                parts = aspect_ratio.split(':')
                if len(parts) == 2:
                    w, h = int(parts[0]), int(parts[1])
                    if w > 0 and h > 0:
                        return f"{w}:{h}"
            except:
                pass
        
        # 默认返回1:1
        return "1:1"
    
    def _create_default_adapted_params(self, workflow_type: WorkflowType, llm_params: Dict[str, Any]) -> AdaptedParameters:
        """创建默认适配参数"""
        return AdaptedParameters(
            workflow_type=workflow_type,
            prompt=llm_params.get('prompt', ''),
            negative_prompt=llm_params.get('negative_prompt', ''),
            width=1024,
            height=1024,
            steps=20,
            guidance=3.5,
            seed=-1,
            original_llm_params=llm_params,
            adaptation_notes=["使用默认参数（未知工作流类型）"]
        )
    
    def get_workflow_requirements(self, workflow_type: WorkflowType) -> Optional[WorkflowParameterRequirements]:
        """获取工作流参数需求"""
        return self.workflow_requirements.get(workflow_type)
    
    def list_supported_workflows(self) -> List[WorkflowType]:
        """列出支持的工作流类型"""
        return list(self.workflow_requirements.keys())


# 全局适配器实例
_global_adapter = None

def get_workflow_parameter_adapter() -> WorkflowParameterAdapter:
    """获取全局工作流参数适配器实例"""
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = WorkflowParameterAdapter()
    return _global_adapter 