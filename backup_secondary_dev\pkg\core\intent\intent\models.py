"""
意图分析数据模型
统一的意图分析数据结构和枚举定义
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, List, Optional


class ContentType(Enum):
    """内容类型枚举"""
    # 文生图类型
    PORTRAIT = "portrait"           # 人像
    LANDSCAPE = "landscape"         # 风景
    ANIME = "anime"                 # 动漫
    REALISTIC = "realistic"         # 写实
    PRODUCT = "product"             # 产品
    ARCHITECTURAL = "architectural" # 建筑
    CONCEPT_ART = "concept_art"     # 概念艺术
    ILLUSTRATION = "illustration"   # 插画
    PHOTOGRAPHY = "photography"     # 摄影
    ABSTRACT = "abstract"           # 抽象
    
    # 图像控制类型
    CONTROLNET = "controlnet"       # ControlNet控制
    REDUX_REFERENCE = "redux_reference" # Redux参考
    KONTEXT = "kontext"             # 图像编辑
    IMAGE_TO_IMAGE = "image_to_image" # 图像到图像
    INPAINTING = "inpainting"       # 图像修复
    UPSCALING = "upscaling"         # 图像放大
    
    # 混合类型
    HYBRID = "hybrid"               # 混合控制
    
    # 默认类型
    DEFAULT = "default"             # 默认
    UNKNOWN = "unknown"             # 未知


class InputMode(Enum):
    """输入模式枚举"""
    TEXT_ONLY = "text_only"         # 纯文本
    IMAGE_CONTROL = "image_control" # 图像控制
    HYBRID = "hybrid"               # 混合输入
    REFERENCE = "reference"         # 参考模式


class QualityLevel(Enum):
    """质量级别枚举"""
    FAST = "fast"                   # 快速
    STANDARD = "standard"           # 标准
    HIGH = "high"                   # 高质量
    ULTRA = "ultra"                 # 超高质量


class WorkflowPriority(Enum):
    """工作流优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class AnalysisContext:
    """分析上下文"""
    user_id: str = ""
    chat_id: str = ""
    has_images: bool = False
    image_count: int = 0
    image_types: List[str] = field(default_factory=list)
    session_history: List[str] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)


@dataclass
class IntentAnalysis:
    """意图分析结果"""
    content_type: ContentType
    confidence: float
    keywords: List[str]
    recommended_workflow: str
    suggested_params: Dict[str, Any]
    
    # 输入模式分析
    input_mode: InputMode = InputMode.TEXT_ONLY
    required_image_types: List[str] = field(default_factory=list)
    min_images: int = 0
    max_images: int = 0
    image_purpose: str = ""
    
    # 输出参数
    aspect_ratio: Optional[str] = None
    quality_level: QualityLevel = QualityLevel.STANDARD
    priority: WorkflowPriority = WorkflowPriority.MEDIUM
    
    # 分析元数据
    analysis_time: float = 0.0
    fallback_reason: str = ""
    alternative_workflows: List[str] = field(default_factory=list)
    
    def is_image_required(self) -> bool:
        """是否需要图片输入"""
        return self.min_images > 0
    
    def is_high_confidence(self, threshold: float = 0.8) -> bool:
        """是否高置信度"""
        return self.confidence >= threshold
    
    def get_workflow_params(self) -> Dict[str, Any]:
        """获取工作流参数"""
        params = self.suggested_params.copy()
        params.update({
            'content_type': self.content_type.value,
            'quality_level': self.quality_level.value,
            'input_mode': self.input_mode.value
        })
        
        if self.aspect_ratio:
            params['aspect_ratio'] = self.aspect_ratio
            
        return params


@dataclass
class WorkflowRecommendation:
    """工作流推荐结果"""
    workflow_type: str
    confidence: float
    reasons: List[str]
    estimated_time: float = 0.0
    estimated_cost: float = 0.0
    requirements: Dict[str, Any] = field(default_factory=dict)
    limitations: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    
    def is_suitable(self, context: AnalysisContext) -> bool:
        """检查是否适合当前上下文"""
        # 检查图片要求
        if 'min_images' in self.requirements:
            if context.image_count < self.requirements['min_images']:
                return False
        
        if 'max_images' in self.requirements:
            if context.image_count > self.requirements['max_images']:
                return False
        
        # 检查图片类型要求
        if 'required_image_types' in self.requirements:
            required_types = self.requirements['required_image_types']
            if required_types and not any(t in context.image_types for t in required_types):
                return False
        
        return True
    
    def get_missing_requirements(self, context: AnalysisContext) -> List[str]:
        """获取缺失的要求"""
        missing = []
        
        if 'min_images' in self.requirements:
            min_images = self.requirements['min_images']
            if context.image_count < min_images:
                missing.append(f"至少需要{min_images}张图片（当前{context.image_count}张）")
        
        if 'required_image_types' in self.requirements:
            required_types = self.requirements['required_image_types']
            if required_types:
                missing_types = [t for t in required_types if t not in context.image_types]
                if missing_types:
                    missing.append(f"需要图片类型: {', '.join(missing_types)}")
        
        return missing


def content_type_from_string(type_str: str) -> ContentType:
    """从字符串转换为ContentType"""
    try:
        return ContentType(type_str.lower())
    except ValueError:
        return ContentType.UNKNOWN


def input_mode_from_string(mode_str: str) -> InputMode:
    """从字符串转换为InputMode"""
    try:
        return InputMode(mode_str.lower())
    except ValueError:
        return InputMode.TEXT_ONLY


def quality_level_from_string(quality_str: str) -> QualityLevel:
    """从字符串转换为QualityLevel"""
    try:
        return QualityLevel(quality_str.lower())
    except ValueError:
        return QualityLevel.STANDARD


def workflow_priority_from_confidence(confidence: float) -> WorkflowPriority:
    """根据置信度确定工作流优先级"""
    if confidence >= 0.9:
        return WorkflowPriority.CRITICAL
    elif confidence >= 0.7:
        return WorkflowPriority.HIGH
    elif confidence >= 0.5:
        return WorkflowPriority.MEDIUM
    else:
        return WorkflowPriority.LOW 