"""
API 认证处理器

负责 ComfyUI.com API 的认证和基础请求处理
"""

import aiohttp
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta


class APIAuthHandler:
    """ComfyUI.com API 认证处理器"""
    
    def __init__(self, api_key: str, auth_token: str):
        self.api_key = api_key
        self.auth_token = auth_token
        self.base_url = "https://comfyui.com/api"
        self.logger = logging.getLogger(__name__)
        self._session: Optional[aiohttp.ClientSession] = None
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建HTTP会话"""
        if self._session is None or self._session.closed:
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "X-API-Key": self.api_key,
                "Content-Type": "application/json"
            }
            self._session = aiohttp.ClientSession(headers=headers)
        return self._session
    
    async def submit_workflow(self, workflow_data: Dict[str, Any]) -> str:
        """
        提交工作流到远程服务器
        
        Args:
            workflow_data: 工作流配置数据
            
        Returns:
            str: 任务ID
        """
        session = await self._get_session()
        url = f"{self.base_url}/workflow/submit"
        
        async with session.post(url, json=workflow_data) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("task_id")
            else:
                error_text = await response.text()
                raise Exception(f"提交工作流失败: {response.status} - {error_text}")
    
    async def get_quota_info(self) -> Dict[str, Any]:
        """获取API配额信息"""
        session = await self._get_session()
        url = f"{self.base_url}/quota"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                self.logger.warning(f"获取配额信息失败: {response.status}")
                return {"quota": "unknown", "used": "unknown"}
    
    async def validate_credentials(self) -> bool:
        """验证API凭据是否有效"""
        try:
            quota_info = await self.get_quota_info()
            return "quota" in quota_info
        except Exception as e:
            self.logger.error(f"验证凭据失败: {e}")
            return False
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close() 