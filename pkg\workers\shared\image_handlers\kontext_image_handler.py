"""
[二次开发] 重构版 KontextImageHandler
只负责流程调度，核心功能委托子模块

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext图片处理器，负责流程调度
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：Kontext图片生成工作流

[迁移说明] 此文件已从 pkg/provider/runners/ 迁移到 pkg/workers/shared/image_handlers/
原因：优化架构层级，将工具类与运行器分离
迁移时间：2025-01-09
"""


from pkg.workers.kontext.kontext_session_manager import KontextSessionManager
from pkg.workers.kontext.kontext_prompt_optimizer import KontextPromptOptimizer
from pkg.workers.kontext.kontext_image_processor import KontextImageProcessor
from pkg.workers.kontext.kontext_workflow_executor import KontextWorkflowExecutor
from pkg.workers.kontext.kontext_comfyui_auth import KontextComfyUIAuth
from pkg.workers.kontext.local_kontext_workflow_manager import LocalKontextWorkflowManager
from pkg.workers.kontext.aspect_optimizer import AspectRatioOptimizer
from pkg.workers.kontext.prompt_upsampler import PromptUpsampler
from ..image_senders import WeChatImageSender
from typing import List, Any, Dict, Optional, AsyncGenerator
import asyncio
from ....provider import entities as llm_entities

class KontextImageHandler:
    def __init__(self, ap, pipeline_config):
        self.ap = ap
        self.pipeline_config = pipeline_config
        self.session_mgr = KontextSessionManager()
        self.prompt_opt = KontextPromptOptimizer()
        self.img_proc = KontextImageProcessor()
        self.auth = KontextComfyUIAuth()
        
        # 初始化新的模块
        self.local_workflow_manager = LocalKontextWorkflowManager()
        self.aspect_optimizer = AspectRatioOptimizer()
        self.prompt_upsampler = PromptUpsampler()

        # 初始化共享的微信图片发送器
        self.wechat_sender = WeChatImageSender(logger=self.ap.logger)

        # API工作流管理器将在需要时动态创建
        self.api_workflow_manager = None

    def _get_workflow_manager(self, mode: str):
        """根据模式获取正确的工作流管理器"""
        if mode == 'api':
            if self.api_workflow_manager is None:
                # 动态创建API工作流管理器
                from ...kontext_api.kontext_api_workflow_manager import KontextAPIManager
                # 这里需要从配置中获取API密钥，暂时使用空字符串
                self.api_workflow_manager = KontextAPIManager("", "")
            return self.api_workflow_manager
        else:
            return self.local_workflow_manager

    def _determine_mode_from_message(self, user_text: str) -> str:
        """
        根据用户消息确定执行模式
        规则：
        - kontext api xxx → API模式（支持有空格和无空格）
        - kontext xxx → 本地模式（默认）
        """
        if not user_text:
            return 'local'  # 默认本地模式

        text_lower = user_text.lower().strip()

        # 检查是否明确指定API模式（支持有空格和无空格）
        if (text_lower.startswith('kontext api ') or
            text_lower.startswith('kontext_api')):
            self.ap.logger.info("用户明确指定使用API模式")
            return 'api'

        # 其他情况使用本地模式
        self.ap.logger.info("使用默认本地模式")
        return 'local'

    def _extract_clean_prompt(self, user_text: str) -> str:
        """
        提取清理后的提示词（移除模式指令）
        支持有空格和无空格两种格式
        """
        if not user_text:
            return ""

        text_lower = user_text.lower().strip()
        original_text = user_text.strip()

        # 移除 "kontext api " 前缀（有空格）
        if text_lower.startswith('kontext api '):
            return original_text[12:].strip()  # 移除 "kontext api "

        # 移除 "kontext api" 前缀（无空格，但后面不是字母）
        if (text_lower.startswith('kontext api') and
            (len(text_lower) == 11 or not text_lower[11].isalpha())):
            return original_text[11:].strip()  # 移除 "kontext api"

        # 移除 "kontext " 前缀（有空格）
        if text_lower.startswith('kontext '):
            return original_text[8:].strip()  # 移除 "kontext "

        # 移除 "kontext" 前缀（无空格，但后面不是英文字母）
        if text_lower.startswith('kontext'):
            if len(text_lower) == 7:
                return ""  # 只有 "kontext"
            elif len(text_lower) > 7:
                next_char = text_lower[7]
                # 如果后面是中文或非英文字母，则移除前缀
                if not next_char.isascii() or not next_char.isalpha():
                    return original_text[7:].strip()

        # 处理 aigen
        if text_lower.startswith('aigen'):
            if len(text_lower) == 5:
                return ""  # 只有 "aigen"
            elif len(text_lower) > 5:
                next_char = text_lower[5]
                # 如果后面是中文或非英文字母，则移除前缀
                if not next_char.isascii() or not next_char.isalpha():
                    return original_text[5:].strip()

        return original_text

    async def handle_kontext_workflow(self, user_text: str, user_images: List[bytes], user_id: str, chat_id: str, query: Any):
        # 0. 检查取消指令
        from ....core.session.states import is_cancel_command
        if is_cancel_command(user_text):
            self.ap.logger.info("❌ 检测到Kontext取消指令")
            self._cleanup_session_after_completion(user_id, chat_id, "用户取消")
            yield llm_entities.Message(role='assistant', content="❌ Kontext工作流已取消")
            return

        # 1. 确定执行模式
        mode = self._determine_mode_from_message(user_text)

        # 2. 提取清理后的提示词
        clean_prompt = self._extract_clean_prompt(user_text)
        
        # 3. 会话管理
        session = self.session_mgr.get_user_session(user_id, chat_id)
        if not session:
            session = self.session_mgr.create_session(user_id, chat_id)
        
        # 4. 解析命令行参数
        no_trans = False
        try:
            from ....core.intent.parameter_parser import parameter_parser
            parsed_params = parameter_parser.parse_user_input(user_text)
            no_trans = parsed_params.no_trans
            if no_trans:
                self.ap.logger.info("🔍 [DEBUG] 检测到 --no-trans 参数，将使用简单翻译模式")
        except Exception as e:
            self.ap.logger.warning(f"参数解析失败: {e}")

        # 5. 提示词优化 - 🔥 修复：使用LLM进行提示词翻译和优化
        try:
            self.ap.logger.info(f"🔍 [DEBUG] 开始LLM提示词优化:")
            self.ap.logger.info(f"🔍 [DEBUG] - 原始提示词: {clean_prompt}")
            self.ap.logger.info(f"🔍 [DEBUG] - 简单翻译模式: {no_trans}")

            # 🔥 关键修复：确保query对象有ap属性，供Kontext优化器使用
            from ....core.workflow.query_utils import ensure_query_has_ap
            ensure_query_has_ap(query, self.ap, self.ap.logger)

            # 🔥 关键修复：使用异步LLM优化方法，传递no_trans参数
            prompt = await self.prompt_opt.optimize_prompt_with_llm(clean_prompt, query, no_trans)

            self.ap.logger.info(f"🔍 [DEBUG] - LLM优化后提示词: {prompt}")
            self.ap.logger.info(f"LLM提示词优化完成: {prompt}")

            # 🔥 添加LLM优化后的提示词消息（纯英文，无前缀）
            yield llm_entities.Message(role='assistant', content=prompt)

        except Exception as e:
            self.ap.logger.error(f"LLM提示词优化失败: {e}")
            # 不使用回退机制，直接报错并终止工作流
            yield llm_entities.Message(role='assistant', content=f"❌ LLM提示词优化失败: {str(e)}")
            return
        
        # 5. 图片处理
        self.ap.logger.info(f"开始验证 {len(user_images)} 张用户图片")
        images = []
        for i, img in enumerate(user_images):
            is_valid = self.img_proc.is_valid_image(img)
            self.ap.logger.info(f"图片 {i+1}: 大小={len(img)} bytes, 有效={is_valid}")
            if is_valid:
                images.append(img)
            else:
                # 显示图片头部信息用于调试
                header = img[:20].hex() if len(img) >= 20 else img.hex()
                self.ap.logger.warning(f"图片 {i+1} 验证失败，头部: {header}")

        self.ap.logger.info(f"图片验证完成: {len(images)}/{len(user_images)} 张有效")
        
        # 6. 选择工作流
        try:
            workflow_manager = self._get_workflow_manager(mode)
            workflow_config = workflow_manager.select_workflow(len(images))
            self.ap.logger.info(f"选择Kontext工作流: {workflow_config.description}")
        except Exception as e:
            self.ap.logger.error(f"选择工作流失败: {e}")
            yield llm_entities.Message(
                role='assistant',
                content=f"选择工作流失败: {str(e)}"
            )
            return
        
        # 7. 确定最佳比例
        try:
            aspect_ratio = self.aspect_optimizer.determine_optimal_ratio(images, clean_prompt)
            self.ap.logger.info(f"确定比例: {aspect_ratio}")
        except Exception as e:
            self.ap.logger.error(f"确定比例失败: {e}")
            aspect_ratio = "3:2"  # 使用默认比例
        
        # 8. 🔥 混合策略：选择性复用统一参数服务的通用参数
        try:
            self.ap.logger.info(f"🔍 [DEBUG] 开始混合参数分析:")

            # 尝试使用统一参数服务提取通用参数（guidance、steps等）
            try:
                from ....core.workflow.unified_parameter_service import get_unified_parameter_service
                from ....core.workflow.unified_routing_system import WorkflowSubType

                unified_param_service = get_unified_parameter_service(self.ap)

                # 根据图片数量确定工作流子类型
                if len(images) == 1:
                    workflow_subtype = WorkflowSubType.KONTEXT_1IMAGE
                elif len(images) == 2:
                    workflow_subtype = WorkflowSubType.KONTEXT_2IMAGE
                elif len(images) >= 3:
                    workflow_subtype = WorkflowSubType.KONTEXT_3IMAGE
                else:
                    workflow_subtype = WorkflowSubType.KONTEXT_1IMAGE

                param_result = await unified_param_service.analyze_user_input(
                    user_text=clean_prompt,
                    query=query,
                    workflow_subtype=workflow_subtype
                )

                if param_result.success and param_result.llm_params.success:
                    # 🔥 只提取通用参数，忽略尺寸相关参数
                    llm_analyzed_params = param_result.llm_params.parameters
                    self.ap.logger.info(f"🔍 [DEBUG] 统一参数服务成功，提取通用参数")

                    generation_params = {
                        'guidance': llm_analyzed_params.get('guidance', 2.5),
                        'steps': llm_analyzed_params.get('steps', 60),
                        'prompt_upsampling': llm_analyzed_params.get('prompt_upsampling', False),
                        'negative_prompt': llm_analyzed_params.get('negative_prompt', ''),
                        'seed': llm_analyzed_params.get('seed', -1)
                    }
                    self.ap.logger.info(f"✅ 使用LLM通用参数: guidance={generation_params.get('guidance')}, steps={generation_params.get('steps')}")
                else:
                    raise Exception("统一参数服务分析失败")

            except Exception as unified_error:
                # 降级到Kontext专用参数优化
                self.ap.logger.warning(f"🔍 [DEBUG] 统一参数服务失败: {unified_error}")
                self.ap.logger.info(f"🔄 降级到Kontext专用参数优化")
                generation_params = self.prompt_upsampler.optimize_generation_params(clean_prompt)
                self.ap.logger.info(f"🔄 Kontext参数优化: guidance={generation_params.get('guidance')}, steps={generation_params.get('steps')}")

        except Exception as e:
            self.ap.logger.error(f"参数分析失败: {e}")
            self.ap.logger.warning(f"🔍 [DEBUG] 参数分析异常，使用默认参数")
            generation_params = {'guidance': 2.5, 'steps': 20, 'prompt_upsampling': False}
        
        # 9. 获取ComfyUI配置
        comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
        api_url = comfyui_config.get('api-url', 'http://localhost:8188')
        timeout = comfyui_config.get('timeout', 300)  # 🔥 临时增加到5分钟，解决超时问题
        
        # 10. 动态创建执行器
        workflow_exec = KontextWorkflowExecutor(mode=mode, api_url=api_url, timeout=timeout)
        self.ap.logger.info(f"🔍 [DEBUG] 创建KontextWorkflowExecutor: mode={mode}, api_url={api_url}, timeout={timeout}")
        self.ap.logger.info(f"🔍 [DEBUG] 执行器类型: {type(workflow_exec.executor).__name__}")
        
        try:
            # 11. 工作流组装与执行
            workflow_data = await workflow_exec.prepare_workflow_data(
                {}, images, prompt, aspect_ratio, generation_params
            )

            # 🔍 增加调试信息
            self.ap.logger.info(f"🔍 [DEBUG] 工作流数据准备完成:")
            self.ap.logger.info(f"🔍 [DEBUG] - 提示词: {workflow_data.get('prompt', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 宽高比: {workflow_data.get('aspect_ratio', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 引导值: {workflow_data.get('guidance', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 步数: {workflow_data.get('steps', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 种子: {workflow_data.get('seed', 'N/A')}")
            images_list = workflow_data.get('images', [])
            self.ap.logger.info(f"🔍 [DEBUG] - 图片数量: {len(images_list)}")
            if images_list:
                for i, img in enumerate(images_list):
                    self.ap.logger.info(f"🔍 [DEBUG] - 图片{i+1}: {len(img)} bytes")

            self.ap.logger.info(f"执行Kontext工作流: {workflow_config.workflow_file}")

            # 🔥 添加"正在生成..."消息
            yield llm_entities.Message(role='assistant', content="🎨 正在生成图片...")

            self.ap.logger.info(f"🔍 [DEBUG] 即将调用workflow_exec.execute_workflow")
            self.ap.logger.info(f"🔍 [DEBUG] - 执行器类型: {type(workflow_exec).__name__}")
            self.ap.logger.info(f"🔍 [DEBUG] - 底层执行器类型: {type(workflow_exec.executor).__name__}")

            try:
                # 🔥 强制调试：直接调用方法前的最后检查
                self.ap.logger.info(f"🔍 [DEBUG] 强制检查 - 即将调用方法")
                self.ap.logger.info(f"🔍 [DEBUG] - workflow_exec对象: {workflow_exec}")
                self.ap.logger.info(f"🔍 [DEBUG] - execute_workflow方法: {hasattr(workflow_exec, 'execute_workflow')}")
                self.ap.logger.info(f"🔍 [DEBUG] - 方法类型: {type(getattr(workflow_exec, 'execute_workflow', None))}")

                import time
                start_time = time.time()
                self.ap.logger.info(f"🔍 [DEBUG] 开始执行工作流，开始时间: {start_time}")

                result = await workflow_exec.execute_workflow(workflow_config.workflow_file, workflow_data)

                end_time = time.time()
                execution_time = end_time - start_time
                self.ap.logger.info(f"🔍 [DEBUG] workflow_exec.execute_workflow 返回成功，执行时间: {execution_time:.2f}秒")
            except Exception as e:
                self.ap.logger.error(f"🔍 [DEBUG] workflow_exec.execute_workflow 异常: {e}")
                import traceback
                self.ap.logger.error(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
                raise

            # 🔍 增加执行结果调试信息
            self.ap.logger.info(f"🔍 [DEBUG] 工作流执行结果:")
            self.ap.logger.info(f"🔍 [DEBUG] - 成功: {result.success}")
            self.ap.logger.info(f"🔍 [DEBUG] - 错误信息: {result.error_message}")
            self.ap.logger.info(f"🔍 [DEBUG] - 图片数据: {'有' if result.image_data else '无'}")
            if result.metadata:
                self.ap.logger.info(f"🔍 [DEBUG] - 元数据: {result.metadata}")

            # 12. 记录模式选择
            self.ap.logger.info(f"Kontext工作流执行完成 - 模式: {mode}, 提示词: {clean_prompt}")

            # 13. 处理结果并发送图片
            if result.success:
                if result.image_data:
                    self.ap.logger.info(f"🔍 [IMAGE] 开始处理图片数据，大小: {len(result.image_data)} bytes")

                    try:
                        # 🔥 关键修复：使用与Flux相同的直接发送方式
                        send_success = await self.send_image_to_wechat(result.image_data, query)
                        self.ap.logger.info(f"🔍 [IMAGE] 图片发送结果: {send_success}")

                        if not send_success:
                            self.ap.logger.error(f"🔍 [IMAGE] 图片发送失败")
                            yield llm_entities.Message(role='assistant', content="❌ 图片发送失败")
                            return

                    except Exception as e:
                        self.ap.logger.error(f"🔍 [IMAGE] 图片发送异常: {e}")
                        import traceback
                        self.ap.logger.error(f"🔍 [IMAGE] 异常堆栈: {traceback.format_exc()}")
                        yield llm_entities.Message(role='assistant', content=f"❌ 图片发送失败: {str(e)}")
                        return

                    # 🔥 新增：保存Kontext工作流数据，支持--again功能
                    try:
                        self.ap.logger.info("🔍 [SAVE] 开始保存Kontext工作流数据")
                        self._save_kontext_workflow_data(
                            workflow_data=workflow_data,
                            user_prompt=clean_prompt,
                            optimized_prompt=prompt,
                            workflow_type=workflow_config.workflow_file,
                            generation_params=generation_params,
                            aspect_ratio=aspect_ratio,
                            images=images,
                            user_id=user_id,
                            chat_id=chat_id,
                            result=result
                        )
                    except Exception as save_error:
                        self.ap.logger.error(f"❌ 保存Kontext工作流数据失败: {save_error}")
                        # 保存失败不影响主流程

                    # 🔥 统一消息格式 - 与Flux工作流保持一致
                    completion_message = self._format_kontext_completion_message(
                        workflow_config.description,
                        generation_params,
                        result
                    )
                    yield llm_entities.Message(role='assistant', content=completion_message)
                else:
                    yield llm_entities.Message(role='assistant', content="✅ Kontext工作流执行成功")

                # 🔥 成功完成后清理会话
                self._cleanup_session_after_completion(user_id, chat_id, "成功完成")
            else:
                # 🔥 增强错误信息，提供诊断建议
                error_message = self._format_kontext_error_message(result.error_message, mode, workflow_config)
                self.ap.logger.error(f"Kontext工作流失败: {result.error_message}")
                yield llm_entities.Message(role='assistant', content=error_message)

                # 🔥 失败后清理会话
                self._cleanup_session_after_completion(user_id, chat_id, "执行失败")

        except Exception as e:
            self.ap.logger.error(f"执行Kontext工作流失败: {e}")
            yield llm_entities.Message(
                role='assistant',
                content=f"❌ 执行工作流失败: {str(e)}"
            )

            # 🔥 异常后清理会话
            self._cleanup_session_after_completion(user_id, chat_id, "异常终止")
        finally:
            # 14. 关闭执行器
            await workflow_exec.close()

            # 🔥 新增：确保清理完整性 - 无论前面是否成功，都尝试清理
            try:
                self._ensure_session_cleanup(user_id, chat_id, "finally块保险清理")
            except Exception as finally_error:
                self.ap.logger.warning(f"⚠️ Finally块清理失败: {finally_error}")

    def _cleanup_session_after_completion(self, user_id: str, chat_id: str, reason: str):
        """
        Kontext工作流完成后清理会话数据

        🔥 优化说明：
        - 简化清理逻辑，避免重复清理
        - 使用单例 KontextSessionManager 确保会话数据同步
        - 统一清理入口，减少冗余代码

        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            reason: 清理原因
        """
        try:
            self.ap.logger.info(f"🧹 开始清理Kontext会话 - 用户: {user_id}, 聊天: {chat_id}, 原因: {reason}")
            self.ap.logger.info(f"🔍 [DEBUG] 当前KontextSessionManager实例ID: {id(self.session_mgr)}")

            # 🔥 优化：统一使用ComfyUI Agent的完整清理方法（包含Kontext会话清理）
            if hasattr(self.ap, 'comfyui_agent'):
                self.ap.logger.info("🎯 使用ComfyUI Agent的统一清理方法")
                self.ap.comfyui_agent._cleanup_session_completely(user_id, chat_id, f"Kontext-{reason}")
                self.ap.logger.info("✅ 统一清理完成")
                return

            # 🔥 备用方案：如果ComfyUI Agent不可用，手动清理

            # 1. 清理主会话管理器（优先）
            try:
                if hasattr(self.ap, 'session_manager'):
                    main_session = self.ap.session_manager.get_session(user_id, chat_id)
                    if main_session:
                        self.ap.logger.info(f"📊 主会话状态: {main_session.state.value}, 图片数: {len(main_session.images)}")

                        # 清理图片和消息数据
                        if hasattr(main_session, 'images'):
                            image_count = len(main_session.images)
                            main_session.images.clear()
                            self.ap.logger.info(f"🖼️ 已清理 {image_count} 张主会话图片")
                        if hasattr(main_session, 'messages'):
                            message_count = len(main_session.messages)
                            main_session.messages.clear()
                            self.ap.logger.info(f"💬 已清理 {message_count} 条主会话消息")
                        if hasattr(main_session, 'prompt'):
                            main_session.prompt = ""
                        if hasattr(main_session, 'quoted_text'):
                            main_session.quoted_text = ""
                        if hasattr(main_session, 'quoted_images'):
                            main_session.quoted_images.clear()
                        if hasattr(main_session, 'parameters'):
                            main_session.parameters.clear()
                        if hasattr(main_session, 'routing_result'):
                            main_session.routing_result = None
                        if hasattr(main_session, 'forced_workflow_file'):
                            main_session.forced_workflow_file = None

                        self.ap.logger.info("🔄 已清理主会话数据")

                        # 🔥 关键：强制删除主会话
                        success = self.ap.session_manager.delete_session(user_id, chat_id)
                        if success:
                            self.ap.logger.info("✅ 主会话已强制删除")
                        else:
                            self.ap.logger.warning("⚠️ 主会话删除失败，但数据已清理")
                    else:
                        self.ap.logger.info("ℹ️ 未找到主会话，可能已被清理")
            except Exception as main_cleanup_error:
                self.ap.logger.error(f"❌ 主会话清理失败: {main_cleanup_error}")

            # 2. 清理Kontext专用会话（使用同一个单例实例）
            try:
                # 🔥 关键修复：确保使用同一个单例实例
                session = self.session_mgr.get_user_session(user_id, chat_id)
                if session:
                    # 清理会话数据
                    if hasattr(session, 'data') and session.data:
                        data_count = len(session.data)
                        session.data.clear()
                        self.ap.logger.info(f"🗑️ 已清理Kontext会话数据 - 数据项: {data_count}")

                    # 移除会话
                    success = self.session_mgr.remove_session(user_id, chat_id)
                    if success:
                        self.ap.logger.info("✅ Kontext会话已移除")
                    else:
                        self.ap.logger.warning("⚠️ Kontext会话移除失败")
                else:
                    self.ap.logger.info("ℹ️ 未找到Kontext会话，可能已被清理")

                # 🔥 添加会话统计信息
                remaining_sessions = self.session_mgr.get_session_count()
                self.ap.logger.info(f"📊 Kontext会话清理后统计 - 剩余会话: {remaining_sessions}")

            except Exception as kontext_cleanup_error:
                self.ap.logger.error(f"❌ Kontext会话清理失败: {kontext_cleanup_error}")

            self.ap.logger.info(f"🎯 Kontext会话清理完成 - 原因: {reason}")

        except Exception as e:
            self.ap.logger.error(f"❌ Kontext会话清理失败: {e}")
            # 最后的保险措施：强制清理
            try:
                # 强制删除主会话
                if hasattr(self.ap, 'session_manager'):
                    self.ap.session_manager.delete_session(user_id, chat_id)
                    self.ap.logger.info("🚨 强制删除主会话成功")

                # 🔥 关键修复：使用同一个单例实例强制清理
                success = self.session_mgr.remove_session(user_id, chat_id)
                if success:
                    self.ap.logger.info("🚨 强制移除Kontext会话成功")
                else:
                    self.ap.logger.warning("🚨 强制移除Kontext会话失败（可能不存在）")

            except Exception as force_error:
                self.ap.logger.error(f"❌ 强制清理也失败: {force_error}")

    def _ensure_session_cleanup(self, user_id: str, chat_id: str, reason: str):
        """
        确保会话清理完整性的轻量级方法

        🔥 新增功能：
        - 作为保险措施，确保Kontext会话被清理
        - 不重复主会话清理，避免冗余操作

        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            reason: 清理原因
        """
        try:
            # 只清理Kontext会话，避免重复操作
            success = self.session_mgr.remove_session(user_id, chat_id)
            if success:
                self.ap.logger.debug(f"🧹 [ENSURE-CLEANUP] Kontext会话清理成功 - 原因: {reason}")
            else:
                self.ap.logger.debug(f"ℹ️ [ENSURE-CLEANUP] 未找到Kontext会话 - 原因: {reason}")
        except Exception as e:
            self.ap.logger.warning(f"⚠️ [ENSURE-CLEANUP] 保险清理失败: {e}")

    def _save_kontext_workflow_data(self, workflow_data: Dict[str, Any], user_prompt: str,
                                   optimized_prompt: str, workflow_type: str,
                                   generation_params: Dict[str, Any], aspect_ratio: str,
                                   images: List[bytes], user_id: str, chat_id: str, result) -> bool:
        """
        保存Kontext工作流数据，支持--again功能

        🔥 新增功能：
        - 与Flux工作流保持一致的保存逻辑
        - 支持Kontext工作流的--again重新生成
        - 保存完整的工作流参数和图片信息

        Args:
            workflow_data: 工作流数据
            user_prompt: 用户原始提示词
            optimized_prompt: 优化后的提示词
            workflow_type: 工作流类型文件名
            generation_params: 生成参数
            aspect_ratio: 宽高比
            images: 输入图片列表
            user_id: 用户ID
            chat_id: 聊天ID
            result: 执行结果

        Returns:
            bool: 是否保存成功
        """
        try:
            self.ap.logger.info(f"🔍 [SAVE] 准备保存Kontext工作流数据 - 用户: {user_id}, 工作流: {workflow_type}")

            # 使用统一Again管理器保存工作流数据
            from ..again_manager import unified_again_manager

            # 准备图片信息
            image_info = {
                'image_count': len(images),
                'images': [f"image_{i+1}.jpg" for i in range(len(images))],  # 图片占位符
                'aspect_ratio': aspect_ratio
            }

            # 准备完整参数
            parameters = {
                'guidance': generation_params.get('guidance', 3.0),
                'steps': generation_params.get('steps', 20),
                'aspect_ratio': aspect_ratio,
                'image_count': len(images)
            }

            # 添加执行时间信息
            if hasattr(result, 'metadata') and result.metadata:
                parameters['execution_time'] = result.metadata.get('execution_time', 0)

            success = unified_again_manager.save_successful_workflow(
                workflow_data=workflow_data,
                user_prompt=user_prompt,
                optimized_prompt=optimized_prompt,
                workflow_type=workflow_type,
                parameters=parameters,
                lora_info={},  # Kontext工作流通常不使用LoRA
                image_info=image_info,
                user_id=user_id,
                chat_id=chat_id
            )

            if success:
                self.ap.logger.info("✅ Kontext工作流数据已保存，支持 --again 功能")
            else:
                self.ap.logger.warning("⚠️ Kontext工作流数据保存失败")

            return success

        except Exception as e:
            self.ap.logger.error(f"❌ 保存Kontext工作流数据异常: {e}")
            import traceback
            self.ap.logger.error(f"🔍 [SAVE] 保存异常详情: {traceback.format_exc()}")
            return False

    def _format_kontext_completion_message(self, workflow_description: str, generation_params: dict, result) -> str:
        """
        格式化Kontext完成消息 - 与Flux工作流保持一致的格式

        Args:
            workflow_description: 工作流描述
            generation_params: 生成参数
            result: 执行结果

        Returns:
            格式化的完成消息
        """
        try:
            content = f"✅ **图片生成完成**\n\n"
            content += f"⚙️ **工作流**: {workflow_description}\n"

            # Kontext参数信息
            if generation_params:
                if 'guidance' in generation_params:
                    content += f"🎯 **引导值**: {generation_params['guidance']}\n"
                if 'steps' in generation_params:
                    content += f"🔄 **步数**: {generation_params['steps']}\n"
                if 'aspect_ratio' in generation_params:
                    content += f"📐 **比例**: {generation_params['aspect_ratio']}\n"

            # 执行时间
            if hasattr(result, 'metadata') and result.metadata:
                execution_time = result.metadata.get('execution_time', 0)
                if execution_time > 0:
                    content += f"⏱️ **耗时**: {execution_time:.1f}秒"

            return content

        except Exception as e:
            self.ap.logger.error(f"格式化Kontext完成消息失败: {e}")
            return "✅ Kontext工作流执行成功，图片已生成"

    async def handle_kontext_api_workflow(self, user_text: str, user_images: List[bytes], user_id: str, chat_id: str, query: Any):
        """处理 Kontext API 工作流"""
        try:
            self.ap.logger.info(f"开始执行 Kontext API 工作流")

            # 使用与本地工作流相同的逻辑，但可能需要不同的执行器
            async for message in self.handle_kontext_workflow(user_text, user_images, user_id, chat_id, query):
                yield message

        except Exception as e:
            self.ap.logger.error(f"Kontext API 工作流执行失败: {e}")
            yield llm_entities.Message(
                role='assistant',
                content=f"❌ Kontext API 工作流执行失败: {str(e)}"
            )

            # 🔥 API工作流异常后清理会话
            self._cleanup_session_after_completion(user_id, chat_id, "API异常终止")

    async def handle_kontext_session_interaction(self, user_text: str, user_images: List[bytes], session: Any, user_id: str, chat_id: str, query: Any):
        async for message in self.handle_kontext_workflow(user_text, user_images, user_id, chat_id, query):
            yield message

    def extract_user_images(self, message: Any) -> List[bytes]:
        return self.img_proc.extract_user_images(message)

    def optimize_prompt(self, prompt: str) -> str:
        return self.prompt_opt.optimize_prompt(prompt)

    def is_authenticated(self) -> bool:
        return self.auth.is_authenticated()

    async def send_image_to_wechat(self, image_data: bytes, query: Any) -> bool:
        """发送图片到微信 - 使用共享的发送器"""
        return await self.wechat_sender.send_image_to_wechat(image_data, query)

    async def create_image_message(self, image_data: bytes):
        """创建图片消息 - 使用共享的实现"""
        return await self.wechat_sender.create_image_message_base64(image_data)

    def get_user_id(self, query: Any) -> str:
        """获取用户ID"""
        try:
            return f"{query.launcher_type.value}_{query.sender_id}"
        except:
            return "unknown_user"
    
    def get_chat_id(self, query: Any) -> str:
        """获取聊天ID"""
        try:
            return f"{query.launcher_type.value}_{query.sender_id}"
        except:
            return "unknown_chat"

    def _format_kontext_error_message(self, error_message: str, mode: str, workflow_config) -> str:
        """格式化Kontext错误消息，提供诊断建议"""
        try:
            message_parts = [
                f"❌ Kontext工作流执行失败"
            ]

            # 添加基本错误信息
            if error_message:
                message_parts.append(f"🔍 错误: {error_message}")

            # 根据错误类型提供诊断建议
            if "ComfyUI服务未启动" in error_message or "无法连接" in error_message:
                message_parts.extend([
                    "",
                    "🔧 可能的解决方案:",
                    "1. 检查ComfyUI服务是否在Linux服务器上正常运行",
                    "2. 确认端口8188是否可访问",
                    "3. 检查网络连接状态"
                ])
            elif "超时" in error_message:
                message_parts.extend([
                    "",
                    "🔧 可能的解决方案:",
                    "1. 检查ComfyUI服务器资源使用情况",
                    "2. 确认工作流文件配置是否正确",
                    "3. 检查是否有其他任务占用GPU资源",
                    f"4. 当前工作流: {workflow_config.workflow_file}"
                ])
            elif "工作流文件不存在" in error_message:
                message_parts.extend([
                    "",
                    "🔧 可能的解决方案:",
                    "1. 检查workflows目录下是否存在对应文件",
                    f"2. 确认文件: {workflow_config.workflow_file}",
                    "3. 检查文件权限和路径配置"
                ])
            else:
                message_parts.extend([
                    "",
                    "🔧 建议:",
                    "1. 检查服务器日志获取详细错误信息",
                    "2. 确认ComfyUI和langbot服务状态",
                    f"3. 执行模式: {mode}"
                ])

            return "\n".join(message_parts)

        except Exception as e:
            self.ap.logger.error(f"格式化Kontext错误消息失败: {e}")
            return f"❌ Kontext工作流执行失败: {error_message}"