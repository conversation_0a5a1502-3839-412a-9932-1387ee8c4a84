apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: modelscope-chat-completions
  label:
    en_US: ModelScope
    zh_Hans: 魔搭社区
  icon: modelscope.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api-inference.modelscope.cn/v1"
    - name: args
      label:
        en_US: Args
        zh_Hans: 附加参数
      type: object
      required: true
      default: {}
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: int
      required: true
      default: 120
execution:
  python:
    path: ./modelscopechatcmpl.py
    attr: ModelScopeChatCompletions
