.cardContainer {
  width: 100%;
  height: 10rem;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 2px 0 rgba(0, 0, 0, 0.2);
  padding: 1.2rem;
  cursor: pointer;
}

.cardContainer:hover {
  box-shadow: 0px 2px 8px 0 rgba(0, 0, 0, 0.1);
}

.iconBasicInfoContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  gap: 0.8rem;
  user-select: none;
  /* background-color: aqua; */
}

.iconImage {
  width: 3.8rem;
  height: 3.8rem;
  margin: 0.2rem;
  border-radius: 50%;
}

.basicInfoContainer {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  min-width: 0;
  width: 100%;
}

.basicInfoText {
  font-size: 1.4rem;
  font-weight: bold;
}

.providerContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.2rem;
}

.providerIcon {
  width: 1.2rem;
  height: 1.2rem;
  margin-top: 0.2rem;
  color: #626262;
}

.providerLabel {
  font-size: 1.2rem;
  font-weight: 600;
  color: #626262;
}

.baseURLContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.2rem;
  width: calc(100% - 3rem);
}

.baseURLIcon {
  width: 1.2rem;
  height: 1.2rem;
  color: #626262;
}

.baseURLText {
  font-size: 1rem;
  width: 100%;
  color: #626262;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.abilitiesContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.4rem;
}

.abilityBadge {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.2rem;
  height: 1.5rem;
  padding: 0.5rem;
  border-radius: 0.8rem;
  background-color: #66baff80;
}

.abilityIcon {
  width: 1rem;
  height: 1rem;
  color: #2288ee;
}

.abilityLabel {
  font-size: 0.8rem;
  font-weight: 400;
  color: #2288ee;
}

.bigText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.4rem;
  font-weight: bold;
  max-width: 100%;
}
