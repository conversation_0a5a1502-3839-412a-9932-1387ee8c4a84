from __future__ import annotations

"""
[二次开发] 管理员同步处理器
负责将用户请求和生成结果同步给管理员

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：管理员同步处理器，同步用户请求和生成结果
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：管理员同步功能

[迁移说明] 此文件已从 pkg/provider/runners/ 迁移到 pkg/workers/shared/sync/
原因：优化架构层级，将工具类与运行器分离
迁移时间：2025-01-09
"""

import os
import tempfile
from datetime import datetime
from typing import Dict, Any

from ....core import app, entities as core_entities
from ....platform.types import message as platform_message


class AdminSyncHandler:
    """管理员同步处理器"""
    
    def __init__(self, ap: app.Application, pipeline_config: dict, query: core_entities.Query):
        self.ap = ap
        self.pipeline_config = pipeline_config
        self.query = query
        
        # 从服务中获取同步配置（如果可用）
        self.admin_sync = None
        try:
            from ....services.admin_sync_service import AdminSyncService
            self.admin_sync = AdminSyncService(pipeline_config, query, ap.logger)
        except Exception as e:
            self.ap.logger.debug(f"管理员同步服务初始化失败: {e}")

    def is_sync_enabled(self) -> bool:
        """检查是否启用了管理员同步"""
        return bool(self.admin_sync and self.admin_sync.is_sync_enabled())

    def get_admin_wxid(self) -> str:
        """获取管理员微信ID"""
        if self.admin_sync:
            return str(self.admin_sync.get_admin_wxid())
        return ""

    async def sync_user_request(self, params: Dict[str, Any]):
        """同步用户请求到管理员"""
        try:
            if not self.is_sync_enabled():
                return
                
            admin_wxid = self.get_admin_wxid()
            if not admin_wxid:
                return
                
            # 构建请求信息
            user_name = "未知用户"
            group_name = "未知群组"
            
            # 从消息事件中获取用户和群组信息
            if hasattr(self.query.message_event, 'sender') and self.query.message_event.sender:  # type: ignore
                sender = self.query.message_event.sender  # type: ignore
                if hasattr(sender, 'member_name') and sender.member_name:
                    user_name = sender.member_name
                elif hasattr(sender, 'nickname') and sender.nickname:
                    user_name = sender.nickname
                    
                if hasattr(sender, 'group') and sender.group and hasattr(sender.group, 'name') and sender.group.name:
                    group_name = sender.group.name
            
            # 构建通知消息
            message_text = (
                f"[图片生成请求]\n"
                f"群组: {group_name[:20]}\n"
                f"用户: {user_name[:10]}\n"
                f"提示词: {params.get('prompt', '未指定')[:50]}"
            )
            
            # 发送通知到管理员
            await self.query.adapter.send_message(
                target_type="friend",
                target_id=admin_wxid,
                message=platform_message.MessageChain([
                    platform_message.Plain(message_text)
                ])
            )
            
            self.ap.logger.info(f"已向管理员同步用户请求: {admin_wxid}")
        except Exception as e:
            self.ap.logger.error(f"同步用户请求到管理员失败: {str(e)}")

    async def sync_generated_image(self, image_data: bytes, params: Dict[str, Any]):
        """同步生成的图片到管理员"""
        try:
            if not self.is_sync_enabled():
                return
                
            admin_wxid = self.get_admin_wxid()
            if not admin_wxid:
                return
                
            # 获取用户和群组信息
            user_name = "未知用户"
            group_name = "未知群组"
            
            if hasattr(self.query.message_event, 'sender') and self.query.message_event.sender:  # type: ignore
                sender = self.query.message_event.sender  # type: ignore
                if hasattr(sender, 'member_name') and sender.member_name:
                    user_name = sender.member_name
                elif hasattr(sender, 'nickname') and sender.nickname:
                    user_name = sender.nickname
                    
                if hasattr(sender, 'group') and sender.group and hasattr(sender.group, 'name') and sender.group.name:
                    group_name = sender.group.name
            
            # 保存图片到临时文件
            temp_path = None
            try:
                # 创建临时文件
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                temp_path = f"temp_admin_sync_{timestamp}.png"
                with open(temp_path, "wb") as f:
                    f.write(image_data)
                
                # 构建描述信息
                description = (
                    f"[生成完成]\n"
                    f"群组: {group_name[:20]}\n"
                    f"用户: {user_name[:10]}\n"
                    f"提示词: {params.get('prompt', '未指定')[:50]}"
                )
                
                # 发送描述信息
                await self.query.adapter.send_message(
                    target_type="friend",
                    target_id=admin_wxid,
                    message=platform_message.MessageChain([
                        platform_message.Plain(description)
                    ])
                )
                
                # 发送图片
                await self.query.adapter.send_message(
                    target_type="friend", 
                    target_id=admin_wxid,
                    message=platform_message.MessageChain([
                        platform_message.Image(path=temp_path)
                    ])
                )
                
                self.ap.logger.info(f"已向管理员同步生成图片: {admin_wxid}")
                
            finally:
                # 清理临时文件
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            self.ap.logger.error(f"同步生成图片到管理员失败: {str(e)}") 