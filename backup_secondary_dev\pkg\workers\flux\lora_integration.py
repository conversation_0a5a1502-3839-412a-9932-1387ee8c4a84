"""
Flux LoRA 集成模块
负责 LoRA 模型选择、应用和工作流集成
这是 Flux 工作流的专有特性
"""

import json
import os
import gc
import weakref
from typing import Dict, Any, Optional, List, Tuple
import logging

from .flux_workflow_models import FluxParameters, LoRAConfig, LoRACategory
from ...workers.shared.shared_lora_manager import SharedLoraManager


class LoRAProcessingError(Exception):
    """LoRA处理统一异常"""
    def __init__(self, message: str, original_error: Exception = None):
        super().__init__(message)
        self.original_error = original_error


class LoRAIntegration:
    """Flux LoRA 集成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.lora_manager = SharedLoraManager()
        
        # 内存管理：使用弱引用避免循环引用
        self._workflow_cache = weakref.WeakValueDictionary()
        self._model_references = weakref.WeakSet()
        
        # LoRA节点模板
        self.lora_node_template = {
            "class_type": "LoraLoader",
            "inputs": {
                "model": None,
                "clip": None,
                "lora_name": "",
                "strength_model": 0.8,
                "strength_clip": 0.8
            }
        }
    
    def __del__(self):
        """析构函数，确保清理资源"""
        self._cleanup_resources()
    
    def _cleanup_resources(self):
        """清理资源，避免内存泄漏"""
        try:
            # 清理缓存
            self._workflow_cache.clear()
            self._model_references.clear()
            
            # 强制垃圾回收
            gc.collect()
            
            self.logger.info("LoRA集成器资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
    
    def _validate_lora_node(self, lora: LoRAConfig, model_input: Tuple[str, int], clip_input: Tuple[str, int]) -> bool:
        """
        验证LoRA节点配置的有效性
        
        Args:
            lora: LoRA配置
            model_input: 模型输入连接
            clip_input: CLIP输入连接
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证LoRA文件名
            if not lora.filename or not lora.filename.strip():
                self.logger.error(f"LoRA文件名无效: {lora.filename}")
                return False
            
            # 验证权重范围
            if not (0.1 <= lora.weight <= 1.0):
                self.logger.error(f"LoRA权重超出范围: {lora.weight}")
                return False
            
            # 验证输入连接
            if not model_input or not clip_input:
                self.logger.error("模型或CLIP输入连接无效")
                return False
            
            # 验证文件是否存在（如果路径可用）
            if lora.file_path and os.path.exists(lora.file_path):
                if not os.path.isfile(lora.file_path):
                    self.logger.error(f"LoRA文件不是有效文件: {lora.file_path}")
                    return False
            else:
                self.logger.warning(f"无法验证LoRA文件存在性: {lora.filename}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"LoRA节点验证失败: {e}")
            return False
    
    def _create_robust_lora_node(self, lora: LoRAConfig, model_input: Tuple[str, int], clip_input: Tuple[str, int]) -> Optional[Dict[str, Any]]:
        """
        创建健壮的LoRA节点，包含验证和错误处理
        
        Args:
            lora: LoRA配置
            model_input: 模型输入连接
            clip_input: CLIP输入连接
            
        Returns:
            Optional[Dict[str, Any]]: LoRA节点配置，验证失败时返回None
        """
        try:
            # 验证LoRA配置
            if not self._validate_lora_node(lora, model_input, clip_input):
                return None
            
            # 创建标准LoRA节点
            lora_node = {
                "class_type": "LoraLoader",
                "inputs": {
                    "model": list(model_input),
                    "clip": list(clip_input),
                    "lora_name": lora.filename,
                    "strength_model": round(lora.weight, 3),  # 限制精度避免浮点误差
                    "strength_clip": round(lora.weight, 3)
                },
                "_meta": {
                    "title": f"LoRA: {lora.name}",
                    "description": f"LoRA模型: {lora.description}"
                }
            }
            
            # 添加内存管理标记
            lora_node["_memory_managed"] = True
            
            self.logger.info(f"成功创建LoRA节点: {lora.name} (权重: {lora.weight})")
            return lora_node
            
        except Exception as e:
            self.logger.error(f"创建LoRA节点失败: {e}")
            return None
    
    async def select_loras_for_prompt(self, params: FluxParameters, max_loras: int = 3,
                                    use_civitai: bool = False, civitai_query: str = "") -> List[LoRAConfig]:
        """
        根据提示词和参数选择合适的LoRA模型（智能动态选择版本）

        Args:
            params: Flux参数
            max_loras: 最大LoRA数量
            use_civitai: 是否使用Civitai搜索
            civitai_query: Civitai搜索关键词

        Returns:
            List[LoRAConfig]: 选择的LoRA配置列表
        """
        try:
            selected_loras = []

            # 第一步：本地模型匹配
            search_query = civitai_query if civitai_query else params.prompt
            # 指定只匹配flux类型的模型
            selected_models = self.lora_manager.get_models_by_trigger(self.lora_manager._tokenize_input(search_query), model_type="flux")

            # 动态决定LoRA数量
            optimal_lora_count = self._determine_optimal_lora_count(selected_models, search_query)
            self.logger.info(f"根据匹配质量，选择 {optimal_lora_count} 个LoRA模型")

            # 如果启用Civitai且本地模型不足，尝试搜索下载
            if use_civitai and len(selected_models) < optimal_lora_count:
                self.logger.info(f"本地模型不足，从Civitai搜索: {search_query}")
                await self._search_and_download_civitai_models(search_query, optimal_lora_count - len(selected_models))
                # 重新获取模型（可能包含新下载的）
                selected_models = self.lora_manager.get_models_by_trigger(self.lora_manager._tokenize_input(search_query))
            
            # 转换为LoRAConfig格式，限制数量
            for model in selected_models[:optimal_lora_count]:
                lora_config = LoRAConfig(
                    name=model.name,
                    filename=model.filename,
                    weight=model.weight,
                    category=self._map_category(model.category.value),
                    trigger_words=model.trigger_words,
                    description=model.description,
                    file_path=model.file_path,
                    is_local=model.is_local,
                    is_active=model.is_active
                )
                
                # 设置Civitai信息
                if model.civitai_id:
                    lora_config.civitai_id = model.civitai_id
                if model.civitai_url:
                    lora_config.civitai_url = model.civitai_url
                if model.rating is not None:
                    lora_config.rating = float(model.rating)
                if model.downloads is not None:
                    lora_config.downloads = int(model.downloads)
                
                selected_loras.append(lora_config)
            
            # 如果没有找到合适的LoRA，添加默认的细节增强LoRA
            if not selected_loras:
                default_lora = self._get_default_detail_lora()
                if default_lora:
                    selected_loras.append(default_lora)
            
            self.logger.info(f"为提示词选择了 {len(selected_loras)} 个LoRA模型")
            for lora in selected_loras:
                self.logger.info(f"  - {lora.name} (权重: {lora.weight}, 分类: {lora.category.value})")
            
            return selected_loras
            
        except Exception as e:
            self.logger.error(f"选择LoRA模型失败: {e}")
            # 返回默认LoRA
            default_lora = self._get_default_detail_lora()
            return [default_lora] if default_lora else []
    
    def apply_loras_to_workflow(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig]) -> Tuple[Dict[str, Any], str]:
        """
        将LoRA模型应用到工作流（Flux优化版本，包含统一错误处理）

        Args:
            workflow_data: 原始工作流数据
            loras: LoRA配置列表

        Returns:
            Tuple[Dict[str, Any], str]: (更新后的工作流数据, 状态消息)
        """
        if not loras:
            self.logger.info("没有LoRA模型需要应用")
            return workflow_data, "✅ 工作流准备完成（无LoRA）"

        try:
            return self._apply_loras_core(workflow_data, loras)
        except Exception as e:
            # 统一错误处理和降级
            return self._handle_lora_failure(workflow_data, loras, e)

    def _apply_loras_core(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig]) -> Tuple[Dict[str, Any], str]:
        """LoRA应用的核心逻辑"""
        try:
            self.logger.info(f"🔧 [DEBUG] 开始应用LoRA到工作流")
            self.logger.info(f"🔧 [DEBUG] 工作流节点数: {len(workflow_data)}")
            self.logger.info(f"🔧 [DEBUG] LoRA数量: {len(loras)}")

            # 安全检查工作流数据
            if not isinstance(workflow_data, dict):
                raise LoRAProcessingError(f"工作流数据类型错误: {type(workflow_data)}")

            # 使用安全拷贝避免污染原始数据
            updated_workflow = self._safe_copy_workflow(workflow_data)
            
            # 查找现有的LoRA节点（包括Flux专用节点）
            existing_lora_nodes = self._find_all_lora_nodes(updated_workflow)
            self.logger.info(f"🔧 [DEBUG] 找到现有LoRA节点: {existing_lora_nodes}")
            
            # 找到模型连接的起始点（Flux工作流中是UNETLoader）
            model_source_node_id = self._find_model_source_node(updated_workflow)
            self.logger.info(f"🔧 [DEBUG] 模型源节点: {model_source_node_id}")
            
            if not model_source_node_id:
                raise LoRAProcessingError("找不到模型源节点，无法应用LoRA")
            
            # 记录要删除的LoRA节点ID，用于后续更新连接
            # 确保 existing_lora_nodes 是列表类型
            if not isinstance(existing_lora_nodes, list):
                self.logger.error(f"❌ existing_lora_nodes 类型错误: {type(existing_lora_nodes)}, 值: {existing_lora_nodes}")
                existing_lora_nodes = []

            deleted_lora_node_ids = existing_lora_nodes.copy()
            self.logger.info(f"🔧 [DEBUG] 将要删除的LoRA节点: {deleted_lora_node_ids}")
            
            # 删除现有的LoRA节点
            for node_id in existing_lora_nodes:
                if node_id in updated_workflow:
                    del updated_workflow[node_id]
                    self.logger.info(f"删除现有LoRA节点: {node_id}")
            
            # 应用LoRA链（使用健壮的节点创建方法）
            last_model_output = (model_source_node_id, 0)
            last_clip_output = (model_source_node_id, 1)
            
            self.logger.info(f"🔧 [DEBUG] 初始模型输出: {last_model_output}")
            self.logger.info(f"🔧 [DEBUG] 初始CLIP输出: {last_clip_output}")
            
            successful_loras = 0
            for i, lora in enumerate(loras):
                # 创建LoRA节点ID
                lora_node_id = f"lora_{i+1}"
                
                # 使用健壮的LoRA节点创建方法
                lora_node = self._create_robust_lora_node(
                    lora,
                    last_model_output,
                    last_clip_output
                )
                
                # 如果节点创建失败，跳过这个LoRA
                if not lora_node:
                    self.logger.warning(f"跳过无效的LoRA: {lora.name}")
                    continue
                
                # 添加到工作流
                updated_workflow[lora_node_id] = lora_node
                
                # 更新输出连接
                last_model_output = (lora_node_id, 0)
                last_clip_output = (lora_node_id, 1)
                
                successful_loras += 1
                self.logger.info(f"应用LoRA {lora.name} 到节点 {lora_node_id}")
                self.logger.info(f"🔧 [DEBUG] 更新后的模型输出: {last_model_output}")
                self.logger.info(f"🔧 [DEBUG] 更新后的CLIP输出: {last_clip_output}")
            
            # 如果没有成功应用任何LoRA，抛出异常
            if successful_loras == 0:
                raise LoRAProcessingError("没有成功应用任何LoRA模型")
            
            self.logger.info(f"🔧 [DEBUG] 最终模型输出: {last_model_output}")
            self.logger.info(f"🔧 [DEBUG] 最终CLIP输出: {last_clip_output}")
            
            # 更新依赖这些输出的节点
            self._update_downstream_connections(
                updated_workflow,
                model_source_node_id,
                last_model_output,
                last_clip_output,
                deleted_lora_node_ids
            )
            
            # 添加内存管理标记
            updated_workflow["_lora_memory_managed"] = True
            updated_workflow["_lora_count"] = successful_loras
            
            self.logger.info(f"成功应用 {successful_loras} 个LoRA模型到工作流")
            
            # 强制垃圾回收，避免内存泄漏
            gc.collect()

            # 成功应用LoRA
            lora_names = [lora.name for lora in loras]
            success_message = f"✅ 成功应用 {len(loras)} 个LoRA模型: {', '.join(lora_names[:2])}{'...' if len(lora_names) > 2 else ''}"

            return updated_workflow, success_message

        except Exception as e:
            # 重新抛出异常，让上层统一处理
            raise LoRAProcessingError(f"LoRA应用过程中发生错误: {str(e)}", e)
    
    def optimize_lora_weights(self, loras: List[LoRAConfig], quality_level: str = "standard") -> List[LoRAConfig]:
        """
        根据质量级别优化LoRA权重
        
        Args:
            loras: LoRA配置列表
            quality_level: 质量级别 ("fast", "standard", "high", "ultra")
            
        Returns:
            List[LoRAConfig]: 优化后的LoRA配置
        """
        optimized_loras = []
        
        # 权重调整系数
        weight_multipliers = {
            "fast": 0.6,      # 快速生成，降低权重
            "standard": 1.0,  # 标准权重
            "high": 1.2,      # 高质量，稍微增加权重
            "ultra": 1.1      # 超高质量，适度增加权重
        }
        
        multiplier = weight_multipliers.get(quality_level, 1.0)
        
        for lora in loras:
            optimized_lora = LoRAConfig(
                name=lora.name,
                filename=lora.filename,
                weight=min(1.0, lora.weight * multiplier),  # 确保不超过1.0
                category=lora.category,
                trigger_words=lora.trigger_words,
                description=lora.description,
                file_path=lora.file_path,
                civitai_id=lora.civitai_id,
                civitai_url=lora.civitai_url,
                rating=lora.rating,
                downloads=lora.downloads,
                is_local=lora.is_local,
                is_active=lora.is_active
            )
            optimized_loras.append(optimized_lora)
        
        self.logger.info(f"为质量级别 '{quality_level}' 优化LoRA权重 (系数: {multiplier})")
        return optimized_loras
    
    def get_lora_statistics(self) -> Dict[str, Any]:
        """
        获取LoRA模型统计信息
        """
        stats = {
            "total_loras": 0,
            "active_loras": 0,
            "local_loras": 0,
            "categories": {},
            "supported_triggers": []
        }
        try:
            flux_stats = {
                "total_loras": 0,
                "active_loras": 0,
                "local_loras": 0,
                "categories": {},
                "supported_triggers": []
            }
            # 尝试获取模型信息
            try:
                models = getattr(self.lora_manager, 'models', {})
                if models:
                    flux_stats["total_loras"] = len(models)
                    flux_stats["active_loras"] = len([m for m in models.values() if getattr(m, 'is_active', False)])
                    flux_stats["local_loras"] = len([m for m in models.values() if getattr(m, 'is_local', False)])
                    # 按分类统计
                    for model in models.values():
                        if hasattr(model, 'category'):
                            category = getattr(model.category, 'value', str(model.category))
                            flux_stats["categories"][category] = flux_stats["categories"].get(category, 0) + 1
                    # 收集触发词
                    all_triggers = set()
                    for model in models.values():
                        if hasattr(model, 'trigger_words'):
                            all_triggers.update(model.trigger_words)
                    flux_stats["supported_triggers"] = list(all_triggers)[:20]  # 限制数量
            except Exception as e:
                self.logger.warning(f"获取LoRA模型信息时出错: {e}")
                # 使用默认值
            # 合并统计信息
            stats.update(flux_stats)
            return stats
        except Exception as e:
            self.logger.error(f"获取LoRA统计失败: {e}")
            return {}
    
    def validate_lora_files(self, loras: List[LoRAConfig]) -> Tuple[List[LoRAConfig], List[str]]:
        """
        验证LoRA文件是否存在
        
        Args:
            loras: LoRA配置列表
            
        Returns:
            Tuple[List[LoRAConfig], List[str]]: (有效的LoRA, 错误信息)
        """
        valid_loras = []
        errors = []
        
        for lora in loras:
            try:
                # 检查本地文件
                if lora.is_local and lora.file_path:
                    if os.path.exists(lora.file_path):
                        valid_loras.append(lora)
                    else:
                        errors.append(f"LoRA文件不存在: {lora.file_path}")
                else:
                    # 远程模型或者没有指定路径，假设有效
                    valid_loras.append(lora)
                    
            except Exception as e:
                errors.append(f"验证LoRA {lora.name} 失败: {e}")
        
        return valid_loras, errors

    async def _search_and_download_civitai_models(self, query: str, max_models: int = 2):
        """
        从Civitai搜索并下载模型

        Args:
            query: 搜索关键词
            max_models: 最大下载模型数量
        """
        try:
            self.logger.info(f"从Civitai搜索模型: {query} (最多 {max_models} 个)")

            # 更新模型信息（搜索）
            result = await self.lora_manager.update_from_civitai(query=query, limit=max_models * 2)
            self.logger.info(f"Civitai搜索结果: {result}")

            # 获取新搜索到的远程模型
            all_models = list(self.lora_manager.lora_models.values())
            remote_models = [m for m in all_models if not m.is_local and m.civitai_id]

            # 按评分排序，选择最好的模型下载
            remote_models.sort(key=lambda x: (x.rating or 0, x.downloads or 0), reverse=True)

            downloaded_count = 0
            for model in remote_models[:max_models]:
                if downloaded_count >= max_models:
                    break

                try:
                    self.logger.info(f"尝试下载模型: {model.name}")
                    local_path = await self.lora_manager.download_civitai_model(model.name)

                    if local_path:
                        self.logger.info(f"成功下载模型: {model.name} -> {local_path}")
                        downloaded_count += 1
                    else:
                        self.logger.warning(f"下载模型失败: {model.name}")

                except Exception as e:
                    self.logger.error(f"下载模型 {model.name} 时出错: {e}")

            if downloaded_count > 0:
                self.logger.info(f"成功下载 {downloaded_count} 个Civitai模型")
            else:
                self.logger.warning("未能下载任何Civitai模型")

        except Exception as e:
            self.logger.error(f"Civitai搜索下载失败: {e}")
    
    def _map_category(self, category_str: str) -> LoRACategory:
        """映射分类字符串到枚举"""
        category_mapping = {
            "detail": LoRACategory.DETAIL,
            "style": LoRACategory.STYLE,
            "character": LoRACategory.CHARACTER,
            "background": LoRACategory.BACKGROUND,
            "lighting": LoRACategory.LIGHTING,
            "texture": LoRACategory.TEXTURE
        }
        return category_mapping.get(category_str, LoRACategory.DETAIL)
    
    def _get_default_detail_lora(self) -> Optional[LoRAConfig]:
        """获取默认的细节增强LoRA"""
        try:
            # 查找细节增强类的LoRA
            try:
                models = getattr(self.lora_manager, 'models', {})
                for model in models.values():
                    if (hasattr(model, 'category') and hasattr(model.category, 'value') and 
                        model.category.value == "detail" and getattr(model, 'is_active', False)):
                        return LoRAConfig(
                            name=model.name,
                            filename=model.filename,
                            weight=0.8,
                            category=LoRACategory.DETAIL,
                            trigger_words=model.trigger_words,
                            description=model.description,
                            file_path=model.file_path,
                            is_local=model.is_local,
                            is_active=model.is_active
                        )
            except AttributeError:
                pass
            
            # 如果没有找到，返回一个通用的默认配置
            return LoRAConfig(
                name="flux_detail_enhancer",
                filename="flux_detail_enhancer.safetensors",
                weight=0.8,
                category=LoRACategory.DETAIL,
                trigger_words=["detail", "high quality", "masterpiece"],
                description="默认细节增强LoRA",
                is_local=False,
                is_active=True
            )
            
        except Exception as e:
            self.logger.error(f"获取默认LoRA失败: {e}")
            return None
    
    def _find_all_lora_nodes(self, workflow_data: Dict[str, Any]) -> List[str]:
        """查找所有类型的LoRA节点（包括Flux专用节点）"""
        lora_nodes = []

        try:
            if not isinstance(workflow_data, dict):
                self.logger.error(f"❌ workflow_data 类型错误: {type(workflow_data)}")
                return []

            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict):
                    class_type = node_data.get("class_type", "")
                    # 标准LoRA节点
                    if class_type in ["LoraLoader", "LoraLoaderModelOnly"]:
                        lora_nodes.append(node_id)
                        self.logger.info(f"🔧 [DEBUG] 找到标准LoRA节点: {node_id} ({class_type})")
                    # Flux专用LoRA节点
                    elif class_type in ["LorasForFluxParams+", "Power Lora Loader (rgthree)"]:
                        lora_nodes.append(node_id)
                        self.logger.info(f"🔧 [DEBUG] 找到Flux专用LoRA节点: {node_id} ({class_type})")

            self.logger.info(f"🔧 [DEBUG] 总共找到 {len(lora_nodes)} 个LoRA节点")
            return lora_nodes

        except Exception as e:
            self.logger.error(f"❌ 查找LoRA节点时发生错误: {e}")
            return []
    
    def _find_model_source_node(self, workflow_data: Dict[str, Any]) -> Optional[str]:
        """查找模型源节点（Flux工作流中是UNETLoader）"""
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                # Flux工作流使用UNETLoader
                if class_type == "UNETLoader":
                    return node_id
                # 兼容其他工作流
                elif class_type == "CheckpointLoaderSimple":
                    return node_id
        return None
    
    def _create_standard_lora_node(self, lora: LoRAConfig, model_input: Tuple[str, int], clip_input: Tuple[str, int]) -> Dict[str, Any]:
        """创建标准LoRA节点"""
        return {
            "class_type": "LoraLoader",
            "inputs": {
                "model": list(model_input),
                "clip": list(clip_input),
                "lora_name": lora.filename,
                "strength_model": lora.weight,
                "strength_clip": lora.weight
            },
            "_meta": {
                "title": f"LoRA: {lora.name}"
            }
        }
    
    def _update_downstream_connections(self, workflow_data: Dict[str, Any],
                                     checkpoint_node_id: str,
                                     final_model_output: Tuple[str, int],
                                     final_clip_output: Tuple[str, int],
                                     deleted_lora_node_ids: List[str]):
        """更新下游节点的连接"""

        # 类型安全检查
        if not isinstance(deleted_lora_node_ids, list):
            self.logger.error(f"❌ deleted_lora_node_ids 类型错误: {type(deleted_lora_node_ids)}, 值: {deleted_lora_node_ids}")
            deleted_lora_node_ids = []

        self.logger.info(f"🔧 [DEBUG] 开始更新下游连接")
        self.logger.info(f"🔧 [DEBUG] 检查点节点: {checkpoint_node_id}")
        self.logger.info(f"🔧 [DEBUG] 最终模型输出: {final_model_output}")
        self.logger.info(f"🔧 [DEBUG] 最终CLIP输出: {final_clip_output}")
        self.logger.info(f"🔧 [DEBUG] 删除的LoRA节点: {deleted_lora_node_ids}")
        self.logger.info(f"🔧 [DEBUG] deleted_lora_node_ids 类型: {type(deleted_lora_node_ids)}")
        
        # 更新所有节点的连接
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and "inputs" in node_data:
                inputs = node_data["inputs"]
                
                # 更新模型连接
                if "model" in inputs:
                    current_connection = inputs["model"]
                    if (isinstance(current_connection, list) and 
                        len(current_connection) >= 2):
                        self.logger.info(f"🔧 [DEBUG] 节点 {node_id} 的模型连接: {current_connection}")
                        
                        # 检查是否连接到被删除的LoRA节点
                        # 安全检查：确保 deleted_lora_node_ids 是可迭代的
                        if (isinstance(deleted_lora_node_ids, (list, tuple, set)) and
                            current_connection[0] in deleted_lora_node_ids and
                            current_connection[1] == 0):
                            inputs["model"] = list(final_model_output)
                            self.logger.info(f"🔧 [DEBUG] 更新节点 {node_id} 的模型连接到: {final_model_output}")
                        # 检查是否直接连接到检查点节点
                        elif current_connection[0] == checkpoint_node_id and current_connection[1] == 0:
                            inputs["model"] = list(final_model_output)
                            self.logger.info(f"🔧 [DEBUG] 更新节点 {node_id} 的模型连接到: {final_model_output}")
                
                # 更新CLIP连接
                if "clip" in inputs:
                    current_connection = inputs["clip"]
                    if (isinstance(current_connection, list) and 
                        len(current_connection) >= 2):
                        self.logger.info(f"🔧 [DEBUG] 节点 {node_id} 的CLIP连接: {current_connection}")
                        
                        # 检查是否连接到被删除的LoRA节点
                        # 安全检查：确保 deleted_lora_node_ids 是可迭代的
                        if (isinstance(deleted_lora_node_ids, (list, tuple, set)) and
                            current_connection[0] in deleted_lora_node_ids and
                            current_connection[1] == 1):
                            inputs["clip"] = list(final_clip_output)
                            self.logger.info(f"🔧 [DEBUG] 更新节点 {node_id} 的CLIP连接到: {final_clip_output}")
                        # 检查是否直接连接到检查点节点
                        elif current_connection[0] == checkpoint_node_id and current_connection[1] == 1:
                            inputs["clip"] = list(final_clip_output)
                            self.logger.info(f"🔧 [DEBUG] 更新节点 {node_id} 的CLIP连接到: {final_clip_output}")
                
                # 更新positive/negative连接 (通常连接到CLIP输出)
                for conn_type in ["positive", "negative"]:
                    if conn_type in inputs:
                        current_connection = inputs[conn_type]
                        if (isinstance(current_connection, list) and 
                            len(current_connection) >= 2):
                            # 如果原来连接到文本编码器，需要更新其CLIP输入
                            pass  # 文本编码器会自动使用更新后的CLIP
        
        self.logger.info(f"🔧 [DEBUG] 下游连接更新完成")
    
    def create_lora_summary(self, loras: List[LoRAConfig]) -> Dict[str, Any]:
        """创建LoRA使用摘要"""
        return {
            "total_loras": len(loras),
            "lora_details": [
                {
                    "name": lora.name,
                    "weight": lora.weight,
                    "category": lora.category.value,
                    "triggers": lora.trigger_words[:3],  # 只显示前3个触发词
                    "is_local": lora.is_local
                }
                for lora in loras
            ],
            "combined_weight": sum(lora.weight for lora in loras),
            "categories_used": list(set(lora.category.value for lora in loras))
        }

    def _determine_optimal_lora_count(self, selected_models: List, search_query: str) -> int:
        """
        根据匹配质量和用户需求动态决定LoRA数量
        
        Args:
            selected_models: 匹配的模型列表
            search_query: 搜索查询
            
        Returns:
            int: 最优的LoRA数量
        """
        if not selected_models:
            return 1  # 至少返回一个默认LoRA
        
        # 基础规则
        base_count = min(len(selected_models), 3)
        
        # 根据查询复杂度调整
        query_words = search_query.split()
        if len(query_words) > 10:  # 复杂查询
            base_count = min(base_count + 1, 4)
        elif len(query_words) < 5:  # 简单查询
            base_count = max(base_count - 1, 1)
        
        # 根据模型质量调整
        high_quality_count = sum(1 for model in selected_models if getattr(model, 'rating', 0) > 4.0)
        if high_quality_count >= 2:
            base_count = min(base_count + 1, 4)
        
        # 根据模型分类多样性调整
        categories = set()
        for model in selected_models[:base_count]:
            if hasattr(model, 'category'):
                categories.add(getattr(model.category, 'value', str(model.category)))
        
        # 如果分类单一，减少数量
        if len(categories) == 1:
            base_count = max(base_count - 1, 1)
        
        return base_count


    def _handle_lora_failure(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig], error: Exception) -> Tuple[Dict[str, Any], str]:
        """
        处理LoRA应用失败，提供降级方案

        Args:
            workflow_data: 原始工作流数据
            loras: 失败的LoRA配置
            error: 原始错误

        Returns:
            Tuple[Dict[str, Any], str]: (原始工作流, 错误消息)
        """
        error_msg = f"LoRA应用失败: {str(error)}"
        self.logger.error(error_msg)

        # 记录详细错误信息
        if hasattr(error, 'original_error') and error.original_error:
            self.logger.error(f"原始错误: {error.original_error}")

        # 记录失败的LoRA信息
        lora_names = [lora.name for lora in loras]
        self.logger.warning(f"以下LoRA模型应用失败，已降级为无LoRA工作流: {lora_names}")

        # 清理资源
        self._cleanup_resources()

        # 返回原始工作流和用户友好的错误消息
        user_message = f"⚠️ LoRA模型应用失败，已降级为标准工作流。失败的模型: {', '.join(lora_names[:2])}{'...' if len(lora_names) > 2 else ''}"

        return workflow_data, user_message

    def _safe_copy_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """安全地复制工作流数据，避免引用问题"""
        try:
            # 验证原始工作流数据
            self._validate_workflow_data(workflow_data, "原始工作流")

            # 深拷贝，避免修改原始数据
            import copy
            copied_data = copy.deepcopy(workflow_data)

            # 验证拷贝后的工作流数据
            self._validate_workflow_data(copied_data, "拷贝后工作流")

            return copied_data
        except Exception as e:
            self.logger.warning(f"深拷贝失败，使用浅拷贝: {e}")
            copied_data = workflow_data.copy()
            self._validate_workflow_data(copied_data, "浅拷贝后工作流")
            return copied_data

    def _validate_workflow_data(self, workflow_data: Dict[str, Any], stage: str):
        """验证工作流数据的完整性，帮助诊断 Impact Pack 问题"""
        try:
            self.logger.info(f"🔧 [DEBUG] 验证{stage}数据完整性")

            # 检查基本结构
            if not isinstance(workflow_data, dict):
                self.logger.error(f"❌ {stage}: 工作流数据不是字典类型: {type(workflow_data)}")
                return

            # 统计节点信息
            total_nodes = len(workflow_data)
            impact_nodes = []
            lora_nodes = []
            invalid_nodes = []

            for node_id, node_data in workflow_data.items():
                # 🔥 关键修复：检查节点数据是否为布尔值
                if isinstance(node_data, bool):
                    self.logger.error(f"❌ {stage}: 发现布尔值节点 {node_id} = {node_data}")
                    invalid_nodes.append((node_id, type(node_data), node_data))
                    continue

                if isinstance(node_data, dict):
                    class_type = node_data.get("class_type", "")

                    # 检查 Impact Pack 节点
                    if "impact" in class_type.lower() or "ImpactPack" in class_type:
                        impact_nodes.append((node_id, class_type))

                    # 检查 LoRA 节点
                    if "lora" in class_type.lower():
                        lora_nodes.append((node_id, class_type))

                    # 检查节点输入是否包含布尔值
                    inputs = node_data.get("inputs", {})
                    for input_name, input_value in inputs.items():
                        if isinstance(input_value, bool):
                            self.logger.warning(f"⚠️ {stage}: 节点 {node_id} 的输入 {input_name} 是布尔值: {input_value}")
                        elif isinstance(input_value, list) and len(input_value) == 2:
                            # 检查连接格式
                            if not (isinstance(input_value[0], str) and isinstance(input_value[1], int)):
                                self.logger.warning(f"⚠️ {stage}: 节点 {node_id} 的连接 {input_name} 格式异常: {input_value}")
                else:
                    # 记录其他异常类型的节点
                    self.logger.error(f"❌ {stage}: 节点 {node_id} 类型异常: {type(node_data)} = {node_data}")
                    invalid_nodes.append((node_id, type(node_data), node_data))

            self.logger.info(f"🔧 [DEBUG] {stage}: 总节点数={total_nodes}, Impact节点数={len(impact_nodes)}, LoRA节点数={len(lora_nodes)}")

            if invalid_nodes:
                self.logger.error(f"❌ {stage}: 发现 {len(invalid_nodes)} 个无效节点: {invalid_nodes}")
                # 抛出异常，阻止使用损坏的工作流
                raise LoRAProcessingError(f"工作流数据损坏：发现 {len(invalid_nodes)} 个无效节点")

            if impact_nodes:
                self.logger.info(f"🔧 [DEBUG] {stage}: Impact节点: {impact_nodes}")

            if lora_nodes:
                self.logger.info(f"🔧 [DEBUG] {stage}: LoRA节点: {lora_nodes}")

        except Exception as e:
            self.logger.error(f"❌ 验证{stage}数据时发生错误: {e}")
            # 如果是我们抛出的异常，重新抛出
            if isinstance(e, LoRAProcessingError):
                raise
            # 其他异常也抛出，确保不使用损坏的数据
            raise LoRAProcessingError(f"工作流数据验证失败: {str(e)}")


# 全局单例
lora_integration: Optional[LoRAIntegration] = None


def get_lora_integration() -> LoRAIntegration:
    """获取LoRA集成器单例"""
    global lora_integration
    if lora_integration is None:
        lora_integration = LoRAIntegration()
    return lora_integration