"""
统一意图分析器
整合所有工作流类型的意图分析功能
"""

import re
import time
from typing import Dict, Any, List, Optional, Tuple
from .models import (
    ContentType, IntentAnalysis, WorkflowRecommendation, AnalysisContext,
    InputMode, QualityLevel, WorkflowPriority,
    workflow_priority_from_confidence
)


class IntentAnalyzer:
    """统一意图分析器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self._init_keyword_mappings()
        self._init_workflow_configs()
    
    def _log(self, message: str, level: str = "info"):
        """日志记录"""
        if self.logger:
            if level == "error":
                self.logger.error(f"[IntentAnalyzer] {message}")
            elif level == "warning":
                self.logger.warning(f"[IntentAnalyzer] {message}")
            else:
                self.logger.info(f"[IntentAnalyzer] {message}")
    
    def _init_keyword_mappings(self):
        """初始化关键词映射"""
        self.keyword_mapping = {
            ContentType.PORTRAIT: {
                "keywords": [
                    "人像", "头像", "肖像", "人物", "脸部", "美女", "帅哥", "女孩", "男孩",
                    "selfie", "portrait", "face", "person", "woman", "man", "girl", "boy",
                    "模特", "演员", "小姐姐", "小哥哥"
                ],
                "weight": 1.0
            },
            ContentType.LANDSCAPE: {
                "keywords": [
                    "风景", "景观", "自然", "山", "海", "天空", "森林", "湖", "河流", "瀑布",
                    "landscape", "nature", "scenery", "mountain", "sea", "ocean", "sky",
                    "forest", "lake", "river", "waterfall", "beach", "sunset", "sunrise"
                ],
                "weight": 1.0
            },
            ContentType.ANIME: {
                "keywords": [
                    "动漫", "二次元", "卡通", "可爱", "萌", "萝莉", "正太", "御姐", 
                    "anime", "manga", "cute", "kawaii", "cartoon", "chibi", "loli",
                    "otaku", "cosplay", "角色", "角色扮演", "二次元风格"
                ],
                "weight": 1.2
            },
            ContentType.REALISTIC: {
                "keywords": [
                    "写实", "真实", "照片", "摄影", "高清", "超清", "4k", "8k",
                    "realistic", "photorealistic", "photo", "photography", "hd", "ultra hd",
                    "真实感", "逼真", "照片级", "摄影级"
                ],
                "weight": 1.1
            },
            ContentType.PRODUCT: {
                "keywords": [
                    "产品", "商品", "物品", "产品图", "商品图", "展示", "广告", "宣传",
                    "product", "item", "object", "commercial", "advertisement", "showcase",
                    "包装", "设计", "品牌", "logo"
                ],
                "weight": 1.0
            },
            ContentType.ARCHITECTURAL: {
                "keywords": [
                    "建筑", "房屋", "房子", "大楼", "别墅", "宫殿", "教堂", "寺庙",
                    "architecture", "building", "house", "villa", "palace", "church",
                    "temple", "interior", "exterior", "室内", "室外", "装修"
                ],
                "weight": 1.0
            },
            ContentType.CONCEPT_ART: {
                "keywords": [
                    "概念", "概念图", "设计图", "草图", "原画", "设定", "世界观",
                    "concept", "concept art", "design", "sketch", "worldbuilding",
                    "角色设计", "场景设计", "机械设计", "服装设计"
                ],
                "weight": 1.0
            },
            ContentType.ILLUSTRATION: {
                "keywords": [
                    "插画", "插图", "绘画", "手绘", "水彩", "油画", "素描", "速写",
                    "illustration", "drawing", "painting", "watercolor", "oil painting",
                    "sketch", "artwork", "artistic", "艺术", "美术"
                ],
                "weight": 1.0
            },
            ContentType.PHOTOGRAPHY: {
                "keywords": [
                    "摄影", "照片", "拍摄", "相机", "镜头", "光圈", "快门", "iso",
                    "photography", "photo", "camera", "lens", "aperture", "shutter",
                    "街拍", "人像摄影", "风景摄影", "纪实摄影", "艺术摄影"
                ],
                "weight": 1.0
            },
            ContentType.ABSTRACT: {
                "keywords": [
                    "抽象", "抽象画", "现代艺术", "当代艺术", "艺术", "创意", "想象",
                    "abstract", "modern art", "contemporary art", "artistic", "creative",
                    "想象力", "创意", "艺术感", "抽象派", "表现主义"
                ],
                "weight": 1.0
            },
            ContentType.KONTEXT: {
                "keywords": [
                    "kontext", "Kontext", "KONTEXT", "编辑", "修改", "改变", "转换",
                    "edit", "modify", "change", "transform", "convert", "调整", "图生图"
                ],
                "weight": 2.0  # 高权重，优先识别
            }
        }
        
        # 质量关键词
        self.quality_keywords = {
            QualityLevel.HIGH: ["高质量", "高清", "超清", "精细", "详细", "高分辨率", "4k", "8k", "high quality", "detailed", "fine"],
            QualityLevel.FAST: ["快速", "简单", "快速生成", "fast", "quick", "simple", "快速模式"],
            QualityLevel.STANDARD: ["标准", "普通", "正常", "standard", "normal", "regular"],
            QualityLevel.ULTRA: ["超高质量", "极致", "顶级", "ultra", "extreme", "premium"]
        }
        
        # 比例关键词
        self.aspect_ratio_keywords = {
            "16:9": ["宽屏", "超宽", "widescreen", "16:9", "电影", "影院"],
            "9:16": ["竖屏", "手机", "mobile", "9:16", "竖向"],
            "1:1": ["正方形", "方形", "square", "1:1"],
            "3:2": ["横版", "横向", "landscape", "3:2"],
            "2:3": ["竖版", "竖向", "portrait", "2:3"],
            "4:3": ["传统", "4:3", "标准"],
            "21:9": ["超宽屏", "电影宽屏", "21:9"]
        }
        
        # 工作流关键词
        self.workflow_keywords = {
            "aigen": ["aigen", "ai生成", "生成图片", "flux", "画图", "绘图", "文生图"],
            "kontext": ["kontext", "图生图", "变换", "修改", "转换", "风格化", "编辑"],
            "kontext_api": ["kontext_api", "远程", "云端", "api", "在线"]
        }
    
    def _init_workflow_configs(self):
        """初始化工作流配置"""
        self.workflow_configs = {
            "aigen": {
                "input_mode": InputMode.TEXT_ONLY,
                "min_images": 0,
                "max_images": 1,
                "supports_text_only": True,
                "default_params": {
                    "steps": 50,
                    "guidance": 7.5,
                    "strength": 0.8
                }
            },
            "kontext": {
                "input_mode": InputMode.IMAGE_CONTROL,
                "min_images": 1,
                "max_images": 3,
                "supports_text_only": False,
                "default_params": {
                    "steps": 60,
                    "guidance": 4.0,
                    "denoise": 0.85
                }
            },
            "kontext_api": {
                "input_mode": InputMode.IMAGE_CONTROL,
                "min_images": 1,
                "max_images": 3,
                "supports_text_only": False,
                "default_params": {
                    "steps": 60,
                    "guidance": 4.0,
                    "denoise": 0.8
                }
            }
        }
    
    def analyze_intent(
        self, 
        user_input: str, 
        context: Optional[AnalysisContext] = None
    ) -> IntentAnalysis:
        """
        分析用户输入意图
        
        Args:
            user_input: 用户输入文本
            context: 分析上下文
            
        Returns:
            意图分析结果
        """
        start_time = time.time()
        
        if context is None:
            context = AnalysisContext()
        
        user_input_lower = user_input.lower()
        self._log(f"分析用户意图: {user_input[:50]}...")
        
        # 1. 检查是否是Kontext请求
        kontext_analysis = self._analyze_kontext_intent(user_input, context)
        if kontext_analysis:
            kontext_analysis.analysis_time = time.time() - start_time
            return kontext_analysis
        
        # 2. 检查ControlNet相关意图
        controlnet_analysis = self._analyze_controlnet_intent(user_input, context)
        if controlnet_analysis:
            controlnet_analysis.analysis_time = time.time() - start_time
            return controlnet_analysis
        
        # 3. 检查Redux参考意图
        redux_analysis = self._analyze_redux_intent(user_input, context)
        if redux_analysis:
            redux_analysis.analysis_time = time.time() - start_time
            return redux_analysis
        
        # 4. 检查混合意图
        hybrid_analysis = self._analyze_hybrid_intent(user_input, context)
        if hybrid_analysis:
            hybrid_analysis.analysis_time = time.time() - start_time
            return hybrid_analysis
        
        # 5. 文生图意图分析
        text_to_image_analysis = self._analyze_text_to_image_intent(user_input, context)
        if text_to_image_analysis:
            text_to_image_analysis.analysis_time = time.time() - start_time
            return text_to_image_analysis
        
        # 6. 默认分析
        default_analysis = self._default_analysis(user_input, context)
        default_analysis.analysis_time = time.time() - start_time
        return default_analysis
    
    def _analyze_kontext_intent(self, user_input: str, context: AnalysisContext) -> Optional[IntentAnalysis]:
        """分析Kontext意图"""
        kontext_keywords = self.keyword_mapping[ContentType.KONTEXT]["keywords"]
        
        # 检查关键词匹配
        found_keywords = []
        for keyword in kontext_keywords:
            if keyword.lower() in user_input.lower():
                found_keywords.append(keyword)
        
        if found_keywords:
            # 根据图片数量选择工作流
            if context.image_count > 0:
                workflow = "kontext"
            else:
                workflow = "kontext_api"  # 默认选择API版本
            
            config = self.workflow_configs[workflow]
            
            return IntentAnalysis(
                content_type=ContentType.KONTEXT,
                confidence=0.95,
                keywords=found_keywords,
                recommended_workflow=workflow,
                suggested_params=config["default_params"].copy(),
                input_mode=config["input_mode"],
                required_image_types=["reference"],
                min_images=config["min_images"],
                max_images=config["max_images"],
                image_purpose="图像编辑参考",
                aspect_ratio=self._analyze_aspect_ratio(user_input),
                quality_level=self._analyze_quality_level(user_input),
                priority=WorkflowPriority.HIGH
            )
        
        return None
    
    def _analyze_controlnet_intent(self, user_input: str, context: AnalysisContext) -> Optional[IntentAnalysis]:
        """分析ControlNet意图"""
        controlnet_patterns = [
            r'controlnet', r'控制', r'姿势', r'pose', r'深度', r'depth',
            r'边缘', r'edge', r'线稿', r'lineart', r'草图', r'sketch'
        ]
        
        found_patterns = []
        for pattern in controlnet_patterns:
            if re.search(pattern, user_input.lower()):
                found_patterns.append(pattern)
        
        if found_patterns:
            return IntentAnalysis(
                content_type=ContentType.CONTROLNET,
                confidence=0.8,
                keywords=found_patterns,
                recommended_workflow="aigen",  # 使用AIGEN工作流
                suggested_params={
                    "steps": 50,
                    "guidance": 7.5,
                    "controlnet_enabled": True
                },
                input_mode=InputMode.IMAGE_CONTROL,
                required_image_types=["control"],
                min_images=1,
                max_images=1,
                image_purpose="ControlNet控制图",
                aspect_ratio=self._analyze_aspect_ratio(user_input),
                quality_level=self._analyze_quality_level(user_input)
            )
        
        return None
    
    def _analyze_redux_intent(self, user_input: str, context: AnalysisContext) -> Optional[IntentAnalysis]:
        """分析Redux参考意图"""
        redux_patterns = [
            r'参考', r'reference', r'基于', r'according to', r'仿照', r'模仿',
            r'类似', r'similar', r'风格', r'style'
        ]
        
        found_patterns = []
        for pattern in redux_patterns:
            if re.search(pattern, user_input.lower()):
                found_patterns.append(pattern)
        
        if found_patterns and context.image_count > 0:
            return IntentAnalysis(
                content_type=ContentType.REDUX_REFERENCE,
                confidence=0.7,
                keywords=found_patterns,
                recommended_workflow="aigen",
                suggested_params={
                    "steps": 50,
                    "guidance": 7.5,
                    "redux_enabled": True
                },
                input_mode=InputMode.REFERENCE,
                required_image_types=["reference"],
                min_images=1,
                max_images=1,
                image_purpose="Redux参考图",
                aspect_ratio=self._analyze_aspect_ratio(user_input),
                quality_level=self._analyze_quality_level(user_input)
            )
        
        return None
    
    def _analyze_hybrid_intent(self, user_input: str, context: AnalysisContext) -> Optional[IntentAnalysis]:
        """分析混合意图"""
        # 检查是否同时包含文本和图像需求
        has_text_intent = len(user_input.strip()) > 10
        has_image_intent = context.image_count > 0
        
        if has_text_intent and has_image_intent:
            # 分析主要内容类型
            content_type = self._analyze_content_type(user_input)
            
            return IntentAnalysis(
                content_type=content_type,
                confidence=0.6,
                keywords=self._extract_keywords(user_input, content_type),
                recommended_workflow="aigen",  # 默认使用AIGEN处理混合输入
                suggested_params={
                    "steps": 50,
                    "guidance": 7.5,
                    "strength": 0.7
                },
                input_mode=InputMode.HYBRID,
                required_image_types=["reference"],
                min_images=0,
                max_images=1,
                image_purpose="混合参考",
                aspect_ratio=self._analyze_aspect_ratio(user_input),
                quality_level=self._analyze_quality_level(user_input)
            )
        
        return None
    
    def _analyze_text_to_image_intent(self, user_input: str, context: AnalysisContext) -> Optional[IntentAnalysis]:
        """分析文生图意图"""
        if len(user_input.strip()) < 3:
            return None
        
        # 分析内容类型
        content_type = self._analyze_content_type(user_input)
        
        if content_type != ContentType.UNKNOWN:
            config = self.workflow_configs["aigen"]
            
            return IntentAnalysis(
                content_type=content_type,
                confidence=0.7,
                keywords=self._extract_keywords(user_input, content_type),
                recommended_workflow="aigen",
                suggested_params=config["default_params"].copy(),
                input_mode=config["input_mode"],
                required_image_types=[],
                min_images=config["min_images"],
                max_images=config["max_images"],
                image_purpose="可选参考图",
                aspect_ratio=self._analyze_aspect_ratio(user_input),
                quality_level=self._analyze_quality_level(user_input)
            )
        
        return None
    
    def _analyze_content_type(self, user_input: str) -> ContentType:
        """分析内容类型"""
        user_input_lower = user_input.lower()
        type_scores = {}
        
        for content_type, config in self.keyword_mapping.items():
            score = 0
            keywords = config["keywords"]
            weight = config["weight"]
            
            for keyword in keywords:
                if keyword.lower() in user_input_lower:
                    score += weight
            
            if score > 0:
                type_scores[content_type] = score
        
        if not type_scores:
            return ContentType.DEFAULT
        
        # 返回得分最高的类型
        return max(type_scores.keys(), key=lambda x: type_scores[x])
    
    def _extract_keywords(self, user_input: str, content_type: ContentType) -> List[str]:
        """提取关键词"""
        if content_type not in self.keyword_mapping:
            return []
        
        user_input_lower = user_input.lower()
        keywords = self.keyword_mapping[content_type]["keywords"]
        found_keywords = []
        
        for keyword in keywords:
            if keyword.lower() in user_input_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _analyze_aspect_ratio(self, user_input: str) -> Optional[str]:
        """分析宽高比"""
        user_input_lower = user_input.lower()
        
        for ratio, keywords in self.aspect_ratio_keywords.items():
            for keyword in keywords:
                if keyword.lower() in user_input_lower:
                    return ratio
        
        # 检查数字比例模式
        ratio_pattern = r'(\d+):(\d+)'
        match = re.search(ratio_pattern, user_input)
        if match:
            width, height = int(match.group(1)), int(match.group(2))
            # 简化比例
            gcd_val = self._gcd(width, height)
            simplified_ratio = f"{width//gcd_val}:{height//gcd_val}"
            return simplified_ratio
        
        return None
    
    def _analyze_quality_level(self, user_input: str) -> QualityLevel:
        """分析质量级别"""
        user_input_lower = user_input.lower()
        
        for quality, keywords in self.quality_keywords.items():
            for keyword in keywords:
                if keyword.lower() in user_input_lower:
                    return quality
        
        return QualityLevel.STANDARD
    
    def _gcd(self, a: int, b: int) -> int:
        """计算最大公约数"""
        while b:
            a, b = b, a % b
        return a
    
    def _default_analysis(self, user_input: str, context: AnalysisContext) -> IntentAnalysis:
        """默认分析"""
        return IntentAnalysis(
            content_type=ContentType.DEFAULT,
            confidence=0.3,
            keywords=[],
            recommended_workflow="aigen",
            suggested_params=self.workflow_configs["aigen"]["default_params"].copy(),
            input_mode=InputMode.TEXT_ONLY,
            fallback_reason="未能识别明确意图，使用默认配置",
            aspect_ratio=self._analyze_aspect_ratio(user_input),
            quality_level=self._analyze_quality_level(user_input)
        )
    
    def recommend_workflow(self, analysis: IntentAnalysis, context: AnalysisContext) -> WorkflowRecommendation:
        """推荐工作流"""
        workflow_type = analysis.recommended_workflow
        config = self.workflow_configs.get(workflow_type, self.workflow_configs["aigen"])
        
        reasons = []
        limitations = []
        alternatives = []
        
        # 分析推荐原因
        if analysis.content_type == ContentType.KONTEXT:
            reasons.append("检测到图像编辑意图")
            if context.image_count == 0:
                limitations.append("需要提供参考图片")
        elif analysis.input_mode == InputMode.IMAGE_CONTROL:
            reasons.append("检测到图像控制需求")
        else:
            reasons.append("适合文本生成图像")
        
        # 分析替代方案
        if workflow_type == "aigen":
            alternatives = ["kontext", "kontext_api"]
        elif workflow_type == "kontext":
            alternatives = ["aigen", "kontext_api"]
        elif workflow_type == "kontext_api":
            alternatives = ["kontext", "aigen"]
        
        return WorkflowRecommendation(
            workflow_type=workflow_type,
            confidence=analysis.confidence,
            reasons=reasons,
            estimated_time=30.0,  # 默认估计时间
            requirements={
                "min_images": config["min_images"],
                "max_images": config["max_images"],
                "required_image_types": analysis.required_image_types
            },
            limitations=limitations,
            alternatives=alternatives
        )
    
    # 🗑️ 旧的图片分类方法已移除
    # 图片分类现在由统一路由系统和工作流管理器中的 _auto_classify_images_by_workflow 方法处理
    # 基于智能路由选择的工作流子类型自动分类，无需复杂的关键词匹配
    
    def generate_image_type_question(self, image_count: int) -> str:
        """
        生成图片类型询问消息
        
        Args:
            image_count: 图片数量
            
        Returns:
            询问消息
        """
        if image_count == 1:
            return ("📷 请告诉我这张图片的用途：\n"
                   "1️⃣ 控制图（用于控制生成图片的姿势、结构等）\n"
                   "2️⃣ 参考图（用于参考风格、色调等）\n"
                   "请回复 1 或 2")
        
        elif image_count == 2:
            return ("📷 请告诉我这两张图片的用途：\n"
                   "1️⃣ 第一张控制图，第二张参考图\n"
                   "2️⃣ 第一张参考图，第二张控制图\n"
                   "3️⃣ 两张都是参考图\n"
                   "4️⃣ 两张都是控制图\n"
                   "请回复 1、2、3 或 4")
        
        else:
            return (f"📷 您上传了{image_count}张图片，请分别说明每张图片的用途：\n"
                   "请按顺序回复，例如：控制图,参考图,控制图\n"
                   "或回复：取消 来重新开始")


# 全局意图分析器实例
intent_analyzer = IntentAnalyzer() 