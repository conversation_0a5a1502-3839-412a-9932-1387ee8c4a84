"""
[二次开发] Kontext 自定义节点处理器
负责 FluxKontextProImageNode 等 Kontext 专有节点的处理
这是 Kontext 工作流的专有特性

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext工作流的自定义节点处理
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-03 通信模块实现
- 依赖关系：依赖Kontext工作流数据模型
"""

import json
import re
from typing import Dict, Any, Optional, List, Tuple
import logging

from .kontext_workflow_models import (
    KontextNodeConfig, KontextParameters, AspectRatio,
    ImageStitchConfig
)


class CustomNodeHandler:
    """Kontext 自定义节点处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 支持的节点类型
        self.supported_nodes = {
            "FluxKontextProImageNode": {
                "description": "Flux.1 Kontext Pro 图像处理节点",
                "required_inputs": ["prompt", "aspect_ratio", "guidance", "steps"],
                "optional_inputs": ["seed", "prompt_upsampling", "input_image"],
                "outputs": ["images"]
            },
            "easy loadImageBase64": {
                "description": "Base64图片加载节点",
                "required_inputs": ["base64_data"],
                "optional_inputs": ["image_output", "save_prefix"],
                "outputs": ["image"]
            },
            "ImageStitch": {
                "description": "图片拼接节点",
                "required_inputs": ["image1", "image2"],
                "optional_inputs": ["direction", "match_image_size", "spacing_width", "spacing_color"],
                "outputs": ["image"]
            },
            "FluxKontextImageScale": {
                "description": "Kontext图片缩放节点",
                "required_inputs": ["image"],
                "optional_inputs": ["scale_factor"],
                "outputs": ["image"]
            }
        }
    
    def validate_kontext_node(self, node_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证 Kontext 节点配置
        
        Args:
            node_data: 节点数据
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            class_type = node_data.get("class_type", "")
            
            if class_type not in self.supported_nodes:
                errors.append(f"不支持的节点类型: {class_type}")
                return False, errors
            
            # 检查必需输入
            node_info = self.supported_nodes[class_type]
            inputs = node_data.get("inputs", {})
            
            for required_input in node_info["required_inputs"]:
                if required_input not in inputs:
                    errors.append(f"缺少必需输入: {required_input}")
            
            # 特殊验证
            if class_type == "FluxKontextProImageNode":
                errors.extend(self._validate_kontext_pro_node(inputs))
            elif class_type == "easy loadImageBase64":
                errors.extend(self._validate_base64_node(inputs))
            elif class_type == "ImageStitch":
                errors.extend(self._validate_stitch_node(inputs))
            
            return len(errors) == 0, errors
            
        except Exception as e:
            errors.append(f"节点验证异常: {e}")
            return False, errors
    
    def _validate_kontext_pro_node(self, inputs: Dict[str, Any]) -> List[str]:
        """验证 FluxKontextProImageNode 节点"""
        errors = []
        
        # 验证提示词
        prompt = inputs.get("prompt", "")
        if not prompt or len(prompt.strip()) == 0:
            errors.append("提示词不能为空")
        
        # 验证宽高比
        aspect_ratio = inputs.get("aspect_ratio", "")
        valid_ratios = [ratio.value for ratio in AspectRatio]
        if aspect_ratio not in valid_ratios:
            errors.append(f"无效的宽高比: {aspect_ratio}, 支持: {valid_ratios}")
        
        # 验证生成参数
        guidance = inputs.get("guidance", 0)
        if not isinstance(guidance, (int, float)) or guidance <= 0:
            errors.append("guidance 必须是正数")
        
        steps = inputs.get("steps", 0)
        if not isinstance(steps, int) or steps <= 0:
            errors.append("steps 必须是正整数")
        
        # 验证 prompt_upsampling
        prompt_upsampling = inputs.get("prompt_upsampling", False)
        if not isinstance(prompt_upsampling, bool):
            errors.append("prompt_upsampling 必须是布尔值")
        
        return errors
    
    def _validate_base64_node(self, inputs: Dict[str, Any]) -> List[str]:
        """验证 easy loadImageBase64 节点"""
        errors = []
        
        # 验证 base64 数据
        base64_data = inputs.get("base64_data", "")
        if not base64_data:
            errors.append("base64_data 不能为空")
        else:
            # 简单的 base64 格式验证
            try:
                import base64
                base64.b64decode(base64_data)
            except Exception:
                errors.append("base64_data 格式无效")
        
        return errors
    
    def _validate_stitch_node(self, inputs: Dict[str, Any]) -> List[str]:
        """验证 ImageStitch 节点"""
        errors = []
        
        # 验证方向
        direction = inputs.get("direction", "")
        valid_directions = ["right", "left", "up", "down"]
        if direction not in valid_directions:
            errors.append(f"无效的拼接方向: {direction}, 支持: {valid_directions}")
        
        # 验证间距
        spacing_width = inputs.get("spacing_width", 0)
        if not isinstance(spacing_width, int) or spacing_width < 0:
            errors.append("spacing_width 必须是非负整数")
        
        return errors
    
    def create_kontext_pro_node(self, node_id: str, params: KontextParameters, 
                               input_image: Optional[str] = None) -> Dict[str, Any]:
        """
        创建 FluxKontextProImageNode 节点
        
        Args:
            node_id: 节点ID
            params: Kontext参数
            input_image: 输入图片节点连接
            
        Returns:
            Dict[str, Any]: 节点配置
        """
        node_config = {
            "class_type": "FluxKontextProImageNode",
            "_meta": {
                "title": "Flux.1 Kontext [pro] Image"
            },
            "inputs": {
                "prompt": params.prompt,
                "aspect_ratio": params.aspect_ratio.value,
                "guidance": params.guidance,
                "steps": params.steps,
                "seed": params.seed if params.seed > 0 else -1,
                "prompt_upsampling": params.prompt_upsampling
            }
        }
        
        # 添加输入图片连接
        if input_image:
            node_config["inputs"]["input_image"] = input_image
        
        return node_config
    
    def create_base64_node(self, node_id: str, base64_data: str, 
                          title: str = "image_input") -> Dict[str, Any]:
        """
        创建 easy loadImageBase64 节点
        
        Args:
            node_id: 节点ID
            base64_data: Base64图片数据
            title: 节点标题
            
        Returns:
            Dict[str, Any]: 节点配置
        """
        return {
            "class_type": "easy loadImageBase64",
            "_meta": {
                "title": title
            },
            "inputs": {
                "base64_data": base64_data,
                "image_output": "Preview",
                "save_prefix": "ComfyUI"
            }
        }
    
    def create_stitch_node(self, node_id: str, image1: List[str], image2: List[str],
                          config: ImageStitchConfig) -> Dict[str, Any]:
        """
        创建 ImageStitch 节点
        
        Args:
            node_id: 节点ID
            image1: 第一张图片连接
            image2: 第二张图片连接
            config: 拼接配置
            
        Returns:
            Dict[str, Any]: 节点配置
        """
        return {
            "class_type": "ImageStitch",
            "_meta": {
                "title": "Image Stitch"
            },
            "inputs": {
                "direction": config.direction,
                "match_image_size": config.match_image_size,
                "spacing_width": config.spacing_width,
                "spacing_color": config.spacing_color,
                "image1": image1,
                "image2": image2
            }
        }
    
    def update_kontext_node_params(self, node_data: Dict[str, Any], 
                                  params: KontextParameters) -> Dict[str, Any]:
        """
        更新 Kontext 节点参数
        
        Args:
            node_data: 原始节点数据
            params: 新参数
            
        Returns:
            Dict[str, Any]: 更新后的节点数据
        """
        updated_node = node_data.copy()
        
        if updated_node.get("class_type") == "FluxKontextProImageNode":
            inputs = updated_node.get("inputs", {})
            
            # 更新参数
            inputs["prompt"] = params.prompt
            inputs["aspect_ratio"] = params.aspect_ratio.value
            inputs["guidance"] = params.guidance
            inputs["steps"] = params.steps
            inputs["seed"] = params.seed if params.seed > 0 else -1
            inputs["prompt_upsampling"] = params.prompt_upsampling
            
            updated_node["inputs"] = inputs
        
        return updated_node
    
    def extract_node_info(self, workflow_data: Dict[str, Any]) -> List[KontextNodeConfig]:
        """
        从工作流中提取节点信息
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            List[KontextNodeConfig]: 节点配置列表
        """
        nodes = []
        
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                title = node_data.get("_meta", {}).get("title", "")
                inputs = node_data.get("inputs", {})
                
                # 确定输出
                outputs = []
                if class_type == "FluxKontextProImageNode":
                    outputs = ["images"]
                elif class_type in ["easy loadImageBase64", "LoadImage"]:
                    outputs = ["image"]
                elif class_type == "ImageStitch":
                    outputs = ["image"]
                elif class_type == "PreviewImage":
                    outputs = ["images"]
                
                # 确定依赖
                dependencies = []
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) >= 2:
                        dependencies.append(input_value[0])
                
                node_config = KontextNodeConfig(
                    node_id=node_id,
                    class_type=class_type,
                    title=title,
                    inputs=inputs,
                    outputs=outputs,
                    dependencies=dependencies
                )
                
                nodes.append(node_config)
        
        return nodes
    
    def find_kontext_nodes(self, workflow_data: Dict[str, Any]) -> List[str]:
        """
        查找工作流中的 Kontext 节点
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            List[str]: Kontext 节点ID列表
        """
        kontext_nodes = []
        
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                if class_type in self.supported_nodes:
                    kontext_nodes.append(node_id)
        
        return kontext_nodes
    
    def get_node_dependencies(self, workflow_data: Dict[str, Any], target_node_id: str) -> List[str]:
        """
        获取节点的依赖关系
        
        Args:
            workflow_data: 工作流数据
            target_node_id: 目标节点ID
            
        Returns:
            List[str]: 依赖节点ID列表
        """
        dependencies = []
        
        if target_node_id not in workflow_data:
            return dependencies
        
        target_node = workflow_data[target_node_id]
        inputs = target_node.get("inputs", {})
        
        for input_name, input_value in inputs.items():
            if isinstance(input_value, list) and len(input_value) >= 2:
                dependency_node_id = input_value[0]
                if dependency_node_id in workflow_data:
                    dependencies.append(dependency_node_id)
        
        return dependencies
    
    def validate_workflow_structure(self, workflow_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证工作流结构
        
        Args:
            workflow_data: 工作流数据
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            # 检查是否有 Kontext 节点
            kontext_nodes = self.find_kontext_nodes(workflow_data)
            if not kontext_nodes:
                errors.append("工作流中没有找到 Kontext 节点")
            
            # 检查每个节点
            for node_id, node_data in workflow_data.items():
                if isinstance(node_data, dict):
                    is_valid, node_errors = self.validate_kontext_node(node_data)
                    if not is_valid:
                        errors.extend([f"节点 {node_id}: {error}" for error in node_errors])
            
            # 检查依赖关系
            for node_id in workflow_data:
                dependencies = self.get_node_dependencies(workflow_data, node_id)
                for dep_id in dependencies:
                    if dep_id not in workflow_data:
                        errors.append(f"节点 {node_id} 依赖的节点 {dep_id} 不存在")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            errors.append(f"工作流结构验证异常: {e}")
            return False, errors
    
    def optimize_node_order(self, nodes: List[KontextNodeConfig]) -> List[KontextNodeConfig]:
        """
        优化节点执行顺序
        
        Args:
            nodes: 节点列表
            
        Returns:
            List[KontextNodeConfig]: 优化后的节点列表
        """
        # 简单的拓扑排序
        node_map = {node.node_id: node for node in nodes}
        in_degree = {node.node_id: 0 for node in nodes}
        
        # 计算入度
        for node in nodes:
            for dep_id in node.dependencies:
                if dep_id in in_degree:
                    in_degree[dep_id] += 1
        
        # 拓扑排序
        queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
        ordered_nodes = []
        
        while queue:
            current_id = queue.pop(0)
            ordered_nodes.append(node_map[current_id])
            
            for node in nodes:
                if current_id in node.dependencies:
                    in_degree[node.node_id] -= 1
                    if in_degree[node.node_id] == 0:
                        queue.append(node.node_id)
        
        return ordered_nodes


# 全局单例
custom_node_handler: Optional[CustomNodeHandler] = None

def get_custom_node_handler() -> CustomNodeHandler:
    """获取自定义节点处理器单例"""
    global custom_node_handler
    if custom_node_handler is None:
        custom_node_handler = CustomNodeHandler()
    return custom_node_handler 