"""
[二次开发] Kontext 多图片输入处理器
负责处理 1-3 张图片的输入、拼接和管理
这是 Kontext 工作流的专有特性

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext工作流的多图片处理和管理
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-03 通信模块实现
- 依赖关系：依赖PIL图像处理库
"""

import base64
import io
from typing import Dict, Any, Optional, List, Tuple
import logging
from PIL import Image

from .kontext_workflow_models import (
    KontextParameters, ImageInputMode, AspectRatio,
    ImageStitchConfig, KontextNodeConfig
)


class MultiImageHandler:
    """Kontext 多图片输入处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 支持的图片数量配置
        self.supported_counts = {
            1: {
                "mode": ImageInputMode.SINGLE,
                "description": "单图输入",
                "max_size": 2048 * 2048,  # 最大像素数
                "formats": ["JPEG", "PNG", "WEBP"]
            },
            2: {
                "mode": ImageInputMode.DUAL,
                "description": "双图输入",
                "max_size": 1024 * 1024,  # 每张图片最大像素数
                "formats": ["JPEG", "PNG", "WEBP"],
                "stitch_config": ImageStitchConfig(
                    direction="right",
                    match_image_size=True,
                    spacing_width=20,
                    spacing_color="white"
                )
            },
            3: {
                "mode": ImageInputMode.TRIPLE,
                "description": "三图输入",
                "max_size": 800 * 800,  # 每张图片最大像素数
                "formats": ["JPEG", "PNG", "WEBP"],
                "stitch_config": ImageStitchConfig(
                    direction="up",
                    match_image_size=True,
                    spacing_width=8,
                    spacing_color="white"
                )
            }
        }
    
    def validate_images(self, images: List[bytes], target_count: int) -> Tuple[bool, List[str]]:
        """
        验证图片列表
        
        Args:
            images: 图片数据列表
            target_count: 目标图片数量
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            # 检查图片数量
            if len(images) == 0:
                errors.append("没有提供图片")
                return False, errors
            
            if len(images) > 3:
                errors.append(f"图片数量过多: {len(images)}, 最多支持3张图片")
                return False, errors
            
            if target_count not in self.supported_counts:
                errors.append(f"不支持的目标图片数量: {target_count}")
                return False, errors
            
            # 验证每张图片
            for i, image_data in enumerate(images):
                is_valid, image_errors = self._validate_single_image(image_data, i)
                if not is_valid:
                    errors.extend(image_errors)
            
            return len(errors) == 0, errors
            
        except Exception as e:
            errors.append(f"图片验证异常: {e}")
            return False, errors
    
    def _validate_single_image(self, image_data: bytes, index: int) -> Tuple[bool, List[str]]:
        """验证单张图片"""
        errors = []

        try:
            # 使用官方统一的基础验证
            from pkg.core.image.utils import validate_image
            is_valid, error_msg = validate_image(image_data, max_size_mb=10)
            if not is_valid:
                errors.append(f"图片 {index + 1}: {error_msg}")
                return False, errors

            # Kontext特定的验证逻辑
            try:
                image = Image.open(io.BytesIO(image_data))

                # 检查格式（更严格的Kontext要求）
                if image.format not in ["JPEG", "PNG", "WEBP"]:
                    errors.append(f"图片 {index + 1}: 不支持的格式 {image.format}")

                # 检查尺寸（Kontext特定要求）
                width, height = image.size
                pixel_count = width * height

                if pixel_count > 4096 * 4096:  # 最大16M像素
                    errors.append(f"图片 {index + 1}: 分辨率过高 ({width}x{height})")

                if width < 64 or height < 64:
                    errors.append(f"图片 {index + 1}: 分辨率过低 ({width}x{height})")

                image.close()

            except Exception as e:
                errors.append(f"图片 {index + 1}: 无法解析图片格式: {e}")
                return False, errors

            return len(errors) == 0, errors

        except Exception as e:
            errors.append(f"图片 {index + 1}: 验证异常: {e}")
            return False, errors
    
    def process_images_for_workflow(self, images: List[bytes], target_count: int,
                                   workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图片并应用到工作流
        
        Args:
            images: 图片数据列表
            target_count: 目标图片数量
            workflow_data: 工作流数据
            
        Returns:
            Dict[str, Any]: 更新后的工作流数据
        """
        try:
            # 验证图片
            is_valid, errors = self.validate_images(images, target_count)
            if not is_valid:
                self.logger.error(f"图片验证失败: {errors}")
                raise ValueError(f"图片验证失败: {errors}")
            
            # 限制图片数量
            processed_images = images[:target_count]
            
            # 根据目标数量处理
            if target_count == 1:
                return self._process_single_image(processed_images[0], workflow_data)
            elif target_count == 2:
                return self._process_dual_images(processed_images, workflow_data)
            elif target_count == 3:
                return self._process_triple_images(processed_images, workflow_data)
            else:
                raise ValueError(f"不支持的目标图片数量: {target_count}")
                
        except Exception as e:
            self.logger.error(f"处理图片失败: {e}")
            raise
    
    def _process_single_image(self, image_data: bytes, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理单张图片"""
        updated_workflow = workflow_data.copy()
        
        # 查找图片输入节点
        image_nodes = self._find_image_input_nodes(updated_workflow)
        
        if image_nodes:
            # 使用第一个图片输入节点
            node_id, node_data = image_nodes[0]
            if node_data.get("class_type") == "easy loadImageBase64":
                # 转换为base64
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                node_data["inputs"]["base64_data"] = image_base64
                self.logger.info(f"设置单张图片到节点 {node_id}")
        
        return updated_workflow
    
    def _process_dual_images(self, images: List[bytes], workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理双张图片"""
        updated_workflow = workflow_data.copy()
        
        # 查找图片输入节点
        image_nodes = self._find_image_input_nodes(updated_workflow)
        
        if len(image_nodes) >= 2:
            # 设置两张图片
            for i, (node_id, node_data) in enumerate(image_nodes[:2]):
                if node_data.get("class_type") == "easy loadImageBase64":
                    image_base64 = base64.b64encode(images[i]).decode('utf-8')
                    node_data["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"设置图片 {i+1} 到节点 {node_id}")
        
        return updated_workflow
    
    def _process_triple_images(self, images: List[bytes], workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理三张图片"""
        updated_workflow = workflow_data.copy()
        
        # 查找图片输入节点
        image_nodes = self._find_image_input_nodes(updated_workflow)
        
        if len(image_nodes) >= 3:
            # 设置三张图片
            for i, (node_id, node_data) in enumerate(image_nodes[:3]):
                if node_data.get("class_type") == "easy loadImageBase64":
                    image_base64 = base64.b64encode(images[i]).decode('utf-8')
                    node_data["inputs"]["base64_data"] = image_base64
                    self.logger.info(f"设置图片 {i+1} 到节点 {node_id}")
        
        return updated_workflow
    
    def _find_image_input_nodes(self, workflow_data: Dict[str, Any]) -> List[Tuple[str, Dict[str, Any]]]:
        """查找图片输入节点"""
        image_nodes = []
        
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                if class_type in ["easy loadImageBase64", "LoadImage"]:
                    # 按标题排序
                    title = node_data.get("_meta", {}).get("title", "")
                    image_nodes.append((node_id, node_data, title))
        
        # 按标题排序，确保正确的顺序
        image_nodes.sort(key=lambda x: x[2])
        
        return [(node_id, node_data) for node_id, node_data, _ in image_nodes]
    
    def create_image_stitch_workflow(self, images: List[bytes], 
                                   stitch_config: ImageStitchConfig) -> Dict[str, Any]:
        """
        创建图片拼接工作流
        
        Args:
            images: 图片数据列表
            stitch_config: 拼接配置
            
        Returns:
            Dict[str, Any]: 拼接工作流数据
        """
        try:
            if len(images) < 2:
                raise ValueError("拼接需要至少2张图片")
            
            workflow = {}
            
            # 创建图片输入节点
            for i, image_data in enumerate(images):
                node_id = f"image_input_{i+1}"
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                
                workflow[node_id] = {
                    "class_type": "easy loadImageBase64",
                    "_meta": {
                        "title": f"image_input_{i+1:02d}"
                    },
                    "inputs": {
                        "base64_data": image_base64,
                        "image_output": "Preview",
                        "save_prefix": "ComfyUI"
                    }
                }
            
            # 创建拼接节点
            if len(images) == 2:
                # 双图拼接
                stitch_node_id = "image_stitch"
                workflow[stitch_node_id] = {
                    "class_type": "ImageStitch",
                    "_meta": {
                        "title": "Image Stitch"
                    },
                    "inputs": {
                        "direction": stitch_config.direction,
                        "match_image_size": stitch_config.match_image_size,
                        "spacing_width": stitch_config.spacing_width,
                        "spacing_color": stitch_config.spacing_color,
                        "image1": ["image_input_01", 0],
                        "image2": ["image_input_02", 0]
                    }
                }
                
                # 创建预览节点
                preview_node_id = "preview"
                workflow[preview_node_id] = {
                    "class_type": "PreviewImage",
                    "_meta": {
                        "title": "Preview Image"
                    },
                    "inputs": {
                        "images": [stitch_node_id, 0]
                    }
                }
                
            elif len(images) == 3:
                # 三图拼接（先拼接前两张，再拼接第三张）
                # 第一次拼接
                stitch1_node_id = "image_stitch_1"
                workflow[stitch1_node_id] = {
                    "class_type": "ImageStitch",
                    "_meta": {
                        "title": "Image Stitch 1"
                    },
                    "inputs": {
                        "direction": "up",
                        "match_image_size": stitch_config.match_image_size,
                        "spacing_width": 8,
                        "spacing_color": stitch_config.spacing_color,
                        "image1": ["image_input_02", 0],
                        "image2": ["image_input_03", 0]
                    }
                }
                
                # 第二次拼接
                stitch2_node_id = "image_stitch_2"
                workflow[stitch2_node_id] = {
                    "class_type": "ImageStitch",
                    "_meta": {
                        "title": "Image Stitch 2"
                    },
                    "inputs": {
                        "direction": "right",
                        "match_image_size": stitch_config.match_image_size,
                        "spacing_width": stitch_config.spacing_width,
                        "spacing_color": stitch_config.spacing_color,
                        "image1": ["image_input_01", 0],
                        "image2": [stitch1_node_id, 0]
                    }
                }
                
                # 创建预览节点
                preview_node_id = "preview"
                workflow[preview_node_id] = {
                    "class_type": "PreviewImage",
                    "_meta": {
                        "title": "Preview Image"
                    },
                    "inputs": {
                        "images": [stitch2_node_id, 0]
                    }
                }
            
            self.logger.info(f"创建了 {len(images)} 张图片的拼接工作流")
            return workflow
            
        except Exception as e:
            self.logger.error(f"创建拼接工作流失败: {e}")
            raise
    
    def analyze_image_compatibility(self, images: List[bytes]) -> Dict[str, Any]:
        """
        分析图片兼容性
        
        Args:
            images: 图片数据列表
            
        Returns:
            Dict[str, Any]: 兼容性分析结果
        """
        analysis = {
            "total_images": len(images),
            "compatible_count": 0,
            "recommended_workflow": None,
            "image_info": [],
            "warnings": [],
            "recommendations": []
        }
        
        try:
            for i, image_data in enumerate(images):
                try:
                    image = Image.open(io.BytesIO(image_data))
                    width, height = image.size
                    aspect_ratio = width / height
                    
                    image_info = {
                        "index": i,
                        "width": width,
                        "height": height,
                        "aspect_ratio": aspect_ratio,
                        "format": image.format,
                        "size_bytes": len(image_data),
                        "pixel_count": width * height
                    }
                    
                    analysis["image_info"].append(image_info)
                    
                    # 检查兼容性
                    if width >= 64 and height >= 64 and width * height <= 4096 * 4096:
                        analysis["compatible_count"] += 1
                    else:
                        analysis["warnings"].append(f"图片 {i+1}: 尺寸不兼容")
                    
                    image.close()
                    
                except Exception as e:
                    analysis["warnings"].append(f"图片 {i+1}: 无法分析 ({e})")
            
            # 推荐工作流
            if analysis["compatible_count"] >= 1:
                analysis["recommended_workflow"] = f"kontext_local_{analysis['compatible_count']}images.json"
                analysis["recommendations"].append(f"推荐使用 {analysis['compatible_count']} 张图片的工作流")
            
            return analysis
            
        except Exception as e:
            analysis["warnings"].append(f"分析异常: {e}")
            return analysis
    
    def optimize_image_order(self, images: List[bytes], target_aspect_ratio: AspectRatio) -> List[bytes]:
        """
        根据目标宽高比优化图片顺序
        
        Args:
            images: 图片数据列表
            target_aspect_ratio: 目标宽高比
            
        Returns:
            List[bytes]: 优化后的图片列表
        """
        try:
            if len(images) <= 1:
                return images
            
            # 分析每张图片的宽高比
            image_ratios = []
            for i, image_data in enumerate(images):
                try:
                    image = Image.open(io.BytesIO(image_data))
                    width, height = image.size
                    ratio = width / height
                    image_ratios.append((i, ratio))
                    image.close()
                except Exception:
                    image_ratios.append((i, 1.0))  # 默认比例
            
            # 根据目标宽高比排序
            target_ratio_value = self._get_ratio_value(target_aspect_ratio)
            
            # 计算与目标比例的差异
            image_ratios.sort(key=lambda x: abs(x[1] - target_ratio_value))
            
            # 重新排序图片
            optimized_images = [images[i] for i, _ in image_ratios]
            
            self.logger.info(f"根据宽高比 {target_aspect_ratio.value} 优化了图片顺序")
            return optimized_images
            
        except Exception as e:
            self.logger.error(f"优化图片顺序失败: {e}")
            return images
    
    def _get_ratio_value(self, aspect_ratio: AspectRatio) -> float:
        """获取宽高比的数值"""
        ratio_mapping = {
            AspectRatio.SQUARE_1_1: 1.0,
            AspectRatio.LANDSCAPE_3_2: 1.5,
            AspectRatio.PORTRAIT_2_3: 0.67,
            AspectRatio.LANDSCAPE_16_9: 1.78,
            AspectRatio.PORTRAIT_9_16: 0.56
        }
        return ratio_mapping.get(aspect_ratio, 1.0)
    
    def get_supported_config(self, image_count: int) -> Optional[Dict[str, Any]]:
        """获取支持的配置"""
        return self.supported_counts.get(image_count)


# 全局单例
multi_image_handler: Optional[MultiImageHandler] = None

def get_multi_image_handler() -> MultiImageHandler:
    """获取多图片处理器单例"""
    global multi_image_handler
    if multi_image_handler is None:
        multi_image_handler = MultiImageHandler()
    return multi_image_handler 